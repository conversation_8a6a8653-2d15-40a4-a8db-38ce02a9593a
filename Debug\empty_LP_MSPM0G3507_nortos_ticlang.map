******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 16:25:34 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001ae1


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00002170  00005e90  R  X
  SRAM                  20200000   00004000  0000047a  00003b86  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002170   00002170    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002008   00002008    r-x .text
  000020c8    000020c8    00000078   00000078    r-- .rodata
  00002140    00002140    00000030   00000030    r-- .cinit
20200000    20200000    0000027b   00000000    rw-
  20200000    20200000    00000257   00000000    rw- .bss
  20200258    20200258    00000023   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002008     
                  000000c0    00000214     imu.o (.text.imu_analysis)
                  000002d4    0000020c     encoder.o (.text.encoder_exti_callback)
                  000004e0    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000006c0    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000007c4    000000f0     openmv.o (.text.UART3_IRQHandler)
                  000008b4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000099c    000000e4     pid.o (.text.incremental_pid)
                  00000a80    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00000b58    000000c8     imu.o (.text.UART1_IRQHandler)
                  00000c20    000000c4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000ce4    000000a8     nrf24l01.o (.text.nrf24l01_spi_read_write)
                  00000d8c    000000a4     nrf24l01.o (.text.nrf24l01_receive_callback)
                  00000e30    00000090     motor.o (.text.left_motor_set_duty)
                  00000ec0    00000090     motor.o (.text.right_motor_set_duty)
                  00000f50    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_6_init)
                  00000fdc    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_7_init)
                  00001068    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000010f4    00000088     empty.o (.text.timerA_callback)
                  0000117c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001200    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00001282    00000002     empty.o (.text.timerB_callback)
                  00001284    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001300    00000064     nrf24l01.o (.text.nrf24l01_read_buf)
                  00001364    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000013c6    00000002     --HOLE-- [fill = 0]
                  000013c8    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001424    0000005c     protocol.o (.text.protocol_analysis)
                  00001480    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  000014d4    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00001520    0000004c     nrf24l01.o (.text.nrf24l01_write_reg)
                  0000156c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000015b4    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000015fc    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_3_init)
                  00001644    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00001688    00000044     encoder.o (.text.encoder_callback)
                  000016cc    00000044     nrf24l01.o (.text.nrf24l01_read_reg)
                  00001710    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_1_init)
                  00001750    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_8_init)
                  00001790    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000017cc    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_12_init)
                  00001808    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001844    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00001880    0000003c     imu.o (.text.get_verify_code)
                  000018bc    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000018f8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001932    00000002     --HOLE-- [fill = 0]
                  00001934    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000196e    00000002     --HOLE-- [fill = 0]
                  00001970    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000019a8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000019dc    00000032     openmv.o (.text.get_verify_code)
                  00001a0e    00000002     --HOLE-- [fill = 0]
                  00001a10    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00001a40    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00001a68    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  00001a90    00000028     debug.o (.text.UART0_IRQHandler)
                  00001ab8    00000028     motor.o (.text.motor_set_duty)
                  00001ae0    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00001b08    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  00001b2c    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001b4e    00000002     --HOLE-- [fill = 0]
                  00001b50    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001b70    00000020     openmv.o (.text.openmv_analysis)
                  00001b90    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00001bae    00000002     --HOLE-- [fill = 0]
                  00001bb0    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00001bcc    0000001c     nrf24l01.o (.text.DL_GPIO_clearInterruptStatus)
                  00001be8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00001c04    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00001c20    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00001c3c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00001c58    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001c74    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00001c90    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00001cac    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00001cc8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00001ce4    0000001c     timer.o (.text.TIMG12_IRQHandler)
                  00001d00    0000001c     timer.o (.text.TIMG8_IRQHandler)
                  00001d1c    0000001a     empty.o (.text.main)
                  00001d36    00000002     --HOLE-- [fill = 0]
                  00001d38    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001d50    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001d68    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00001d80    00000018     nrf24l01.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00001d98    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00001db0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001dc8    00000018     nrf24l01.o (.text.DL_GPIO_setPins)
                  00001de0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00001df8    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  00001e10    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  00001e28    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  00001e40    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00001e58    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001e70    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001e88    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00001ea0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00001eb8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00001ed0    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00001ee6    00000016     nrf24l01.o (.text.DL_GPIO_readPins)
                  00001efc    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00001f12    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00001f28    00000014     nrf24l01.o (.text.DL_GPIO_clearPins)
                  00001f3c    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00001f50    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00001f64    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00001f78    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00001f8c    00000014     debug.o (.text.DL_UART_receiveData)
                  00001fa0    00000014     imu.o (.text.DL_UART_receiveData)
                  00001fb4    00000014     openmv.o (.text.DL_UART_receiveData)
                  00001fc8    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00001fda    00000012     timer.o (.text.DL_Timer_getPendingInterrupt)
                  00001fec    00000012     debug.o (.text.DL_UART_getPendingInterrupt)
                  00001ffe    00000012     imu.o (.text.DL_UART_getPendingInterrupt)
                  00002010    00000012     openmv.o (.text.DL_UART_getPendingInterrupt)
                  00002022    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00002034    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00002046    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00002058    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  00002068    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002078    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002088    0000000c     empty.o (.text.GROUP1_IRQHandler)
                  00002094    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000209e    0000000a     nrf24l01.o (.text.nrf24l01_spi_delay)
                  000020a8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000020b0    00000006     libc.a : exit.c.obj (.text:abort)
                  000020b6    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000020ba    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000020be    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000020c2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000020c6    00000002     --HOLE-- [fill = 0]

.cinit     0    00002140    00000030     
                  00002140    0000000c     (__TI_handler_table)
                  0000214c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002154    00000008     (.cinit..data.load) [load image, compression = lzss]
                  0000215c    00000010     (__TI_cinit_table)
                  0000216c    00000004     --HOLE-- [fill = 0]

.rodata    0    000020c8    00000078     
                  000020c8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_12TimerConfig)
                  000020dc    00000014     ti_msp_dl_config.o (.rodata.gTIMER_8TimerConfig)
                  000020f0    0000000a     ti_msp_dl_config.o (.rodata.gSPI_1_config)
                  000020fa    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00002104    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  0000210e    0000000a     ti_msp_dl_config.o (.rodata.gUART_3Config)
                  00002118    00000008     ti_msp_dl_config.o (.rodata.gPWM_6Config)
                  00002120    00000008     ti_msp_dl_config.o (.rodata.gPWM_7Config)
                  00002128    00000003     ti_msp_dl_config.o (.rodata.gPWM_6ClockConfig)
                  0000212b    00000003     ti_msp_dl_config.o (.rodata.gPWM_7ClockConfig)
                  0000212e    00000003     ti_msp_dl_config.o (.rodata.gTIMER_12ClockConfig)
                  00002131    00000003     ti_msp_dl_config.o (.rodata.gTIMER_8ClockConfig)
                  00002134    00000002     ti_msp_dl_config.o (.rodata.gSPI_1_clockConfig)
                  00002136    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00002138    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  0000213a    00000002     ti_msp_dl_config.o (.rodata.gUART_3ClockConfig)
                  0000213c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000257     UNINITIALIZED
                  20200000    000000a0     (.common:gPWM_6Backup)
                  202000a0    000000a0     (.common:gPWM_7Backup)
                  20200140    00000030     (.common:gUART_3Backup)
                  20200170    0000002c     (.common:left_speed_pid)
                  2020019c    0000002c     (.common:right_speed_pid)
                  202001c8    00000028     (.common:gSPI_1Backup)
                  202001f0    00000020     (.common:receive_buffer)
                  20200210    0000000c     (.common:acceleration)
                  2020021c    0000000c     (.common:angle)
                  20200228    0000000c     (.common:gyroscope)
                  20200234    0000000b     imu.o (.bss.buffer)
                  2020023f    00000008     openmv.o (.bss.rx_buffer)
                  20200247    00000004     (.common:openmvData)
                  2020024b    00000001     openmv.o (.bss.data)
                  2020024c    00000002     (.common:adcValue1)
                  2020024e    00000002     (.common:adcValue2)
                  20200250    00000002     (.common:medAdcValue1)
                  20200252    00000002     (.common:medAdcValue2)
                  20200254    00000002     (.common:target_speed)
                  20200256    00000001     (.common:data)

.data      0    20200258    00000023     UNINITIALIZED
                  20200258    00000004     imu.o (.data.imu_analysis.angle_z)
                  2020025c    00000004     imu.o (.data.imu_analysis.last_angle_z)
                  20200260    00000004     imu.o (.data.k)
                  20200264    00000004     encoder.o (.data.left_distance)
                  20200268    00000004     encoder.o (.data.right_distance)
                  2020026c    00000002     encoder.o (.data.left_counter)
                  2020026e    00000002     encoder.o (.data.left_speed)
                  20200270    00000002     encoder.o (.data.right_counter)
                  20200272    00000002     encoder.o (.data.right_speed)
                  20200274    00000001     nrf24l01.o (.data.irq)
                  20200275    00000001     imu.o (.data.n)
                  20200276    00000001     openmv.o (.data.n)
                  20200277    00000001     nrf24l01.o (.data.receive_flag)
                  20200278    00000001     imu.o (.data.state)
                  20200279    00000001     openmv.o (.data.state)
                  2020027a    00000001     debug.o (.data.uart_data)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             2686   116       408    
       empty.o                        176    0         90     
       startup_mspm0g350x_ticlang.o   8      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         2870   308       498    
                                                              
    .\drivers\
       imu.o                          830    0         62     
       nrf24l01.o                     704    0         34     
       encoder.o                      666    0         16     
       openmv.o                       360    0         15     
       motor.o                        328    0         0      
       timer.o                        74     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         2962   0         127    
                                                              
    .\soft\
       pid.o                          228    0         0      
       protocol.o                     92     0         8      
       debug.o                        78     0         1      
    +--+------------------------------+------+---------+---------+
       Total:                         398    0         9      
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588    0         0      
       dl_uart.o                      90     0         0      
       dl_spi.o                       86     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         774    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       divsf3.S.obj                   130    0         0      
       comparesf2.S.obj               118    0         0      
       aeabi_fcmp.S.obj               98     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         884    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      44        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   8184   352       1146   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000215c records: 2, size/record: 8, table size: 16
	.bss: load addr=0000214c, load size=00000008 bytes, run addr=20200000, run size=00000257 bytes, compression=zero_init
	.data: load addr=00002154, load size=00000008 bytes, run addr=20200258, run size=00000023 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00002140 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
000020b7  ADC0_IRQHandler                 
000020b7  ADC1_IRQHandler                 
000020b7  AES_IRQHandler                  
000020ba  C$$EXIT                         
000020b7  CANFD0_IRQHandler               
000020b7  DAC0_IRQHandler                 
00002095  DL_Common_delayCycles           
00001645  DL_SPI_init                     
00001fc9  DL_SPI_setClockConfig           
000006c1  DL_Timer_initFourCCPWMMode      
000008b5  DL_Timer_initTimerMode          
00001c91  DL_Timer_setCaptCompUpdateMethod
00001e89  DL_Timer_setCaptureCompareOutCtl
00002069  DL_Timer_setCaptureCompareValue 
00001cad  DL_Timer_setClockConfig         
0000156d  DL_UART_init                    
00002023  DL_UART_setClockConfig          
000020b7  DMA_IRQHandler                  
000020b7  Default_Handler                 
000020b7  GROUP0_IRQHandler               
00002089  GROUP1_IRQHandler               
000020bb  HOSTexit                        
000020b7  HardFault_Handler               
000020b7  I2C0_IRQHandler                 
000020b7  I2C1_IRQHandler                 
000020b7  NMI_Handler                     
000020b7  PendSV_Handler                  
000020b7  RTC_IRQHandler                  
000020bf  Reset_Handler                   
000020b7  SPI0_IRQHandler                 
000020b7  SPI1_IRQHandler                 
000020b7  SVC_Handler                     
000004e1  SYSCFG_DL_GPIO_init             
00000f51  SYSCFG_DL_PWM_6_init            
00000fdd  SYSCFG_DL_PWM_7_init            
00001711  SYSCFG_DL_SPI_1_init            
00001b2d  SYSCFG_DL_SYSCTL_init           
00002079  SYSCFG_DL_SYSTICK_init          
000017cd  SYSCFG_DL_TIMER_12_init         
00001751  SYSCFG_DL_TIMER_8_init          
000015b5  SYSCFG_DL_UART_0_init           
00001481  SYSCFG_DL_UART_1_init           
000015fd  SYSCFG_DL_UART_3_init           
000013c9  SYSCFG_DL_init                  
00000c21  SYSCFG_DL_initPower             
000020b7  SysTick_Handler                 
000020b7  TIMA0_IRQHandler                
000020b7  TIMA1_IRQHandler                
000020b7  TIMG0_IRQHandler                
00001ce5  TIMG12_IRQHandler               
000020b7  TIMG6_IRQHandler                
000020b7  TIMG7_IRQHandler                
00001d01  TIMG8_IRQHandler                
00002035  TI_memcpy_small                 
00001a91  UART0_IRQHandler                
00000b59  UART1_IRQHandler                
000020b7  UART2_IRQHandler                
000007c5  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
0000215c  __TI_CINIT_Base                 
0000216c  __TI_CINIT_Limit                
0000216c  __TI_CINIT_Warm                 
00002140  __TI_Handler_Table_Base         
0000214c  __TI_Handler_Table_Limit        
000018bd  __TI_auto_init_nobinit_nopinit  
00001285  __TI_decompress_lzss            
00002047  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00001f13  __TI_zero_init_nomemset         
00000a8b  __addsf3                        
00001971  __aeabi_f2iz                    
00000a8b  __aeabi_fadd                    
00001365  __aeabi_fcmpeq                  
000013a1  __aeabi_fcmpge                  
000013b5  __aeabi_fcmpgt                  
0000138d  __aeabi_fcmple                  
00001379  __aeabi_fcmplt                  
00001201  __aeabi_fdiv                    
00001069  __aeabi_fmul                    
00000a81  __aeabi_fsub                    
00001809  __aeabi_i2f                     
000020a9  __aeabi_memcpy                  
000020a9  __aeabi_memcpy4                 
000020a9  __aeabi_memcpy8                 
ffffffff  __binit__                       
000018f9  __cmpsf2                        
00001201  __divsf3                        
000018f9  __eqsf2                         
00001971  __fixsfsi                       
00001809  __floatsisf                     
00001845  __gesf2                         
00001845  __gtsf2                         
000018f9  __lesf2                         
000018f9  __ltsf2                         
UNDEFED   __mpu_init                      
00001935  __muldsi3                       
00001069  __mulsf3                        
000018f9  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00000a81  __subsf3                        
00001ae1  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000020c3  _system_pre_init                
000020b1  abort                           
20200210  acceleration                    
2020024c  adcValue1                       
2020024e  adcValue2                       
2020021c  angle                           
ffffffff  binit                           
20200256  data                            
00001689  encoder_callback                
000002d5  encoder_exti_callback           
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
202001c8  gSPI_1Backup                    
20200140  gUART_3Backup                   
20200228  gyroscope                       
000000c1  imu_analysis                    
0000099d  incremental_pid                 
00000000  interruptVectors                
20200274  irq                             
20200260  k                               
2020026c  left_counter                    
20200264  left_distance                   
00000e31  left_motor_set_duty             
2020026e  left_speed                      
20200170  left_speed_pid                  
00001d1d  main                            
20200250  medAdcValue1                    
20200252  medAdcValue2                    
00001ab9  motor_set_duty                  
00000d8d  nrf24l01_receive_callback       
00000ce5  nrf24l01_spi_read_write         
20200247  openmvData                      
00001b71  openmv_analysis                 
00001425  protocol_analysis               
202001f0  receive_buffer                  
20200277  receive_flag                    
20200270  right_counter                   
20200268  right_distance                  
00000ec1  right_motor_set_duty            
20200272  right_speed                     
2020019c  right_speed_pid                 
20200254  target_speed                    
000010f5  timerA_callback                 
00001283  timerB_callback                 
2020027a  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  imu_analysis                    
00000200  __STACK_SIZE                    
000002d5  encoder_exti_callback           
000004e1  SYSCFG_DL_GPIO_init             
000006c1  DL_Timer_initFourCCPWMMode      
000007c5  UART3_IRQHandler                
000008b5  DL_Timer_initTimerMode          
0000099d  incremental_pid                 
00000a81  __aeabi_fsub                    
00000a81  __subsf3                        
00000a8b  __addsf3                        
00000a8b  __aeabi_fadd                    
00000b59  UART1_IRQHandler                
00000c21  SYSCFG_DL_initPower             
00000ce5  nrf24l01_spi_read_write         
00000d8d  nrf24l01_receive_callback       
00000e31  left_motor_set_duty             
00000ec1  right_motor_set_duty            
00000f51  SYSCFG_DL_PWM_6_init            
00000fdd  SYSCFG_DL_PWM_7_init            
00001069  __aeabi_fmul                    
00001069  __mulsf3                        
000010f5  timerA_callback                 
00001201  __aeabi_fdiv                    
00001201  __divsf3                        
00001283  timerB_callback                 
00001285  __TI_decompress_lzss            
00001365  __aeabi_fcmpeq                  
00001379  __aeabi_fcmplt                  
0000138d  __aeabi_fcmple                  
000013a1  __aeabi_fcmpge                  
000013b5  __aeabi_fcmpgt                  
000013c9  SYSCFG_DL_init                  
00001425  protocol_analysis               
00001481  SYSCFG_DL_UART_1_init           
0000156d  DL_UART_init                    
000015b5  SYSCFG_DL_UART_0_init           
000015fd  SYSCFG_DL_UART_3_init           
00001645  DL_SPI_init                     
00001689  encoder_callback                
00001711  SYSCFG_DL_SPI_1_init            
00001751  SYSCFG_DL_TIMER_8_init          
000017cd  SYSCFG_DL_TIMER_12_init         
00001809  __aeabi_i2f                     
00001809  __floatsisf                     
00001845  __gesf2                         
00001845  __gtsf2                         
000018bd  __TI_auto_init_nobinit_nopinit  
000018f9  __cmpsf2                        
000018f9  __eqsf2                         
000018f9  __lesf2                         
000018f9  __ltsf2                         
000018f9  __nesf2                         
00001935  __muldsi3                       
00001971  __aeabi_f2iz                    
00001971  __fixsfsi                       
00001a91  UART0_IRQHandler                
00001ab9  motor_set_duty                  
00001ae1  _c_int00_noargs                 
00001b2d  SYSCFG_DL_SYSCTL_init           
00001b71  openmv_analysis                 
00001c91  DL_Timer_setCaptCompUpdateMethod
00001cad  DL_Timer_setClockConfig         
00001ce5  TIMG12_IRQHandler               
00001d01  TIMG8_IRQHandler                
00001d1d  main                            
00001e89  DL_Timer_setCaptureCompareOutCtl
00001f13  __TI_zero_init_nomemset         
00001fc9  DL_SPI_setClockConfig           
00002023  DL_UART_setClockConfig          
00002035  TI_memcpy_small                 
00002047  __TI_decompress_none            
00002069  DL_Timer_setCaptureCompareValue 
00002079  SYSCFG_DL_SYSTICK_init          
00002089  GROUP1_IRQHandler               
00002095  DL_Common_delayCycles           
000020a9  __aeabi_memcpy                  
000020a9  __aeabi_memcpy4                 
000020a9  __aeabi_memcpy8                 
000020b1  abort                           
000020b7  ADC0_IRQHandler                 
000020b7  ADC1_IRQHandler                 
000020b7  AES_IRQHandler                  
000020b7  CANFD0_IRQHandler               
000020b7  DAC0_IRQHandler                 
000020b7  DMA_IRQHandler                  
000020b7  Default_Handler                 
000020b7  GROUP0_IRQHandler               
000020b7  HardFault_Handler               
000020b7  I2C0_IRQHandler                 
000020b7  I2C1_IRQHandler                 
000020b7  NMI_Handler                     
000020b7  PendSV_Handler                  
000020b7  RTC_IRQHandler                  
000020b7  SPI0_IRQHandler                 
000020b7  SPI1_IRQHandler                 
000020b7  SVC_Handler                     
000020b7  SysTick_Handler                 
000020b7  TIMA0_IRQHandler                
000020b7  TIMA1_IRQHandler                
000020b7  TIMG0_IRQHandler                
000020b7  TIMG6_IRQHandler                
000020b7  TIMG7_IRQHandler                
000020b7  UART2_IRQHandler                
000020ba  C$$EXIT                         
000020bb  HOSTexit                        
000020bf  Reset_Handler                   
000020c3  _system_pre_init                
00002140  __TI_Handler_Table_Base         
0000214c  __TI_Handler_Table_Limit        
0000215c  __TI_CINIT_Base                 
0000216c  __TI_CINIT_Limit                
0000216c  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200140  gUART_3Backup                   
20200170  left_speed_pid                  
2020019c  right_speed_pid                 
202001c8  gSPI_1Backup                    
202001f0  receive_buffer                  
20200210  acceleration                    
2020021c  angle                           
20200228  gyroscope                       
20200247  openmvData                      
2020024c  adcValue1                       
2020024e  adcValue2                       
20200250  medAdcValue1                    
20200252  medAdcValue2                    
20200254  target_speed                    
20200256  data                            
20200260  k                               
20200264  left_distance                   
20200268  right_distance                  
2020026c  left_counter                    
2020026e  left_speed                      
20200270  right_counter                   
20200272  right_speed                     
20200274  irq                             
20200277  receive_flag                    
2020027a  uart_data                       
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[164 symbols]
