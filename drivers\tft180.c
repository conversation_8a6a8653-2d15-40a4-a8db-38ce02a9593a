//
// TFT180 LCD Driver Implementation
//

#include "tft180.h"
#include <stdarg.h>
#include <string.h>
#include <math.h>

// 字体数据引用 (使用OLED的字体)
extern const uint8_t oled_font_6x8[][6];

/**
 * @brief SPI写入一个字节
 * @param data 要写入的数据
 */
void tft180_spi_write_byte(uint8_t data)
{
    // 等待SPI空闲
    while (DL_SPI_isBusy(SPI_1_INST));

    // 发送数据
    DL_SPI_transmitData8(SPI_1_INST, data);

    // 等待发送完成
    while (DL_SPI_isBusy(SPI_1_INST));
}

/**
 * @brief 写入命令
 * @param cmd 命令字节
 */
void tft180_write_cmd(uint8_t cmd)
{
    TFT180_DC_LOW();    // 命令模式
    TFT180_CS_LOW();    // 选中设备
    tft180_spi_write_byte(cmd);
    TFT180_CS_HIGH();   // 取消选中
}

/**
 * @brief 写入数据
 * @param data 数据字节
 */
void tft180_write_data(uint8_t data)
{
    TFT180_DC_HIGH();   // 数据模式
    TFT180_CS_LOW();    // 选中设备
    tft180_spi_write_byte(data);
    TFT180_CS_HIGH();   // 取消选中
}

/**
 * @brief 写入16位数据
 * @param data 16位数据
 */
void tft180_write_data_16bit(uint16_t data)
{
    TFT180_DC_HIGH();   // 数据模式
    TFT180_CS_LOW();    // 选中设备
    tft180_spi_write_byte(data >> 8);   // 高字节
    tft180_spi_write_byte(data & 0xFF); // 低字节
    TFT180_CS_HIGH();   // 取消选中
}

/**
 * @brief 设置显示区域
 * @param x1 起始X坐标
 * @param y1 起始Y坐标
 * @param x2 结束X坐标
 * @param y2 结束Y坐标
 */
void tft180_set_region(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2)
{
    // 设置列地址
    tft180_write_cmd(ST7735_CASET);
    tft180_write_data(0x00);
    tft180_write_data(x1);
    tft180_write_data(0x00);
    tft180_write_data(x2);
    
    // 设置行地址
    tft180_write_cmd(ST7735_RASET);
    tft180_write_data(0x00);
    tft180_write_data(y1);
    tft180_write_data(0x00);
    tft180_write_data(y2);
    
    // 开始写入RAM
    tft180_write_cmd(ST7735_RAMWR);
}

/**
 * @brief 初始化TFT180显示屏
 */
void tft180_init(void)
{
    // 硬件复位
    TFT180_RES_LOW();
    delay_ms(10);
    TFT180_RES_HIGH();
    delay_ms(120);
    
    // 软件复位
    tft180_write_cmd(ST7735_SWRESET);
    delay_ms(150);
    
    // 退出睡眠模式
    tft180_write_cmd(ST7735_SLPOUT);
    delay_ms(500);
    
    // 帧率控制
    tft180_write_cmd(ST7735_FRMCTR1);
    tft180_write_data(0x01);
    tft180_write_data(0x2C);
    tft180_write_data(0x2D);
    
    tft180_write_cmd(ST7735_FRMCTR2);
    tft180_write_data(0x01);
    tft180_write_data(0x2C);
    tft180_write_data(0x2D);
    
    tft180_write_cmd(ST7735_FRMCTR3);
    tft180_write_data(0x01);
    tft180_write_data(0x2C);
    tft180_write_data(0x2D);
    tft180_write_data(0x01);
    tft180_write_data(0x2C);
    tft180_write_data(0x2D);
    
    // 显示反转控制
    tft180_write_cmd(ST7735_INVCTR);
    tft180_write_data(0x07);
    
    // 电源控制
    tft180_write_cmd(ST7735_PWCTR1);
    tft180_write_data(0xA2);
    tft180_write_data(0x02);
    tft180_write_data(0x84);
    
    tft180_write_cmd(ST7735_PWCTR2);
    tft180_write_data(0xC5);
    
    tft180_write_cmd(ST7735_PWCTR3);
    tft180_write_data(0x0A);
    tft180_write_data(0x00);
    
    tft180_write_cmd(ST7735_PWCTR4);
    tft180_write_data(0x8A);
    tft180_write_data(0x2A);
    
    tft180_write_cmd(ST7735_PWCTR5);
    tft180_write_data(0x8A);
    tft180_write_data(0xEE);
    
    // VCOM控制
    tft180_write_cmd(ST7735_VMCTR1);
    tft180_write_data(0x0E);
    
    // 关闭反转
    tft180_write_cmd(ST7735_INVOFF);
    
    // 内存访问控制
    tft180_write_cmd(ST7735_MADCTL);
    tft180_write_data(0xC8);
    
    // 颜色模式设置为16位
    tft180_write_cmd(ST7735_COLMOD);
    tft180_write_data(0x05);
    
    // 开启显示
    tft180_write_cmd(ST7735_DISPON);
    delay_ms(100);
    
    // 清屏
    tft180_clear(TFT180_BLACK);
    
    // 设置背光为最大亮度
    tft180_set_backlight(6400);
}

/**
 * @brief 设置背光亮度
 * @param brightness 亮度值 (0-6400)
 */
void tft180_set_backlight(uint16_t brightness)
{
    if (brightness > 6400) brightness = 6400;
    DL_TimerG_setCaptureCompareValue(PWM_7_INST, brightness, DL_TIMER_CC_0_INDEX);
}

/**
 * @brief 清屏
 * @param color 填充颜色
 */
void tft180_clear(uint16_t color)
{
    tft180_fill_rect(0, 0, TFT180_WIDTH, TFT180_HEIGHT, color);
}

/**
 * @brief 画点
 * @param x X坐标
 * @param y Y坐标
 * @param color 颜色
 */
void tft180_draw_pixel(uint8_t x, uint8_t y, uint16_t color)
{
    if (x >= TFT180_WIDTH || y >= TFT180_HEIGHT) return;
    
    tft180_set_region(x, y, x, y);
    tft180_write_data_16bit(color);
}

/**
 * @brief 填充矩形
 * @param x X坐标
 * @param y Y坐标
 * @param w 宽度
 * @param h 高度
 * @param color 颜色
 */
void tft180_fill_rect(uint8_t x, uint8_t y, uint8_t w, uint8_t h, uint16_t color)
{
    if (x >= TFT180_WIDTH || y >= TFT180_HEIGHT) return;
    if (x + w > TFT180_WIDTH) w = TFT180_WIDTH - x;
    if (y + h > TFT180_HEIGHT) h = TFT180_HEIGHT - y;
    
    tft180_set_region(x, y, x + w - 1, y + h - 1);
    
    TFT180_DC_HIGH();   // 数据模式
    TFT180_CS_LOW();    // 选中设备
    
    for (uint16_t i = 0; i < w * h; i++) {
        tft180_spi_write_byte(color >> 8);   // 高字节
        tft180_spi_write_byte(color & 0xFF); // 低字节
    }
    
    TFT180_CS_HIGH();   // 取消选中
}

/**
 * @brief 画线
 * @param x1 起始X坐标
 * @param y1 起始Y坐标
 * @param x2 结束X坐标
 * @param y2 结束Y坐标
 * @param color 颜色
 */
void tft180_draw_line(uint8_t x1, uint8_t y1, uint8_t x2, uint8_t y2, uint16_t color)
{
    int16_t dx = x2 - x1;
    int16_t dy = y2 - y1;
    int16_t ux = ((dx > 0) << 1) - 1;
    int16_t uy = ((dy > 0) << 1) - 1;
    int16_t x = x1, y = y1, eps;

    dx = dx > 0 ? dx : -dx;
    dy = dy > 0 ? dy : -dy;

    if (dx > dy) {
        eps = dx / 2;
        for (int16_t i = 0; i <= dx; i++) {
            tft180_draw_pixel(x, y, color);
            eps += dy;
            if ((eps << 1) >= dx) {
                y += uy;
                eps -= dx;
            }
            x += ux;
        }
    } else {
        eps = dy / 2;
        for (int16_t i = 0; i <= dy; i++) {
            tft180_draw_pixel(x, y, color);
            eps += dx;
            if ((eps << 1) >= dy) {
                x += ux;
                eps -= dy;
            }
            y += uy;
        }
    }
}

/**
 * @brief 画矩形框
 * @param x X坐标
 * @param y Y坐标
 * @param w 宽度
 * @param h 高度
 * @param color 颜色
 */
void tft180_draw_rect(uint8_t x, uint8_t y, uint8_t w, uint8_t h, uint16_t color)
{
    tft180_draw_line(x, y, x + w - 1, y, color);           // 上边
    tft180_draw_line(x, y + h - 1, x + w - 1, y + h - 1, color); // 下边
    tft180_draw_line(x, y, x, y + h - 1, color);           // 左边
    tft180_draw_line(x + w - 1, y, x + w - 1, y + h - 1, color); // 右边
}

/**
 * @brief 画圆
 * @param x 圆心X坐标
 * @param y 圆心Y坐标
 * @param r 半径
 * @param color 颜色
 */
void tft180_draw_circle(uint8_t x, uint8_t y, uint8_t r, uint16_t color)
{
    int16_t a = 0, b = r;
    int16_t d = 3 - (r << 1);

    while (a <= b) {
        tft180_draw_pixel(x - b, y - a, color);
        tft180_draw_pixel(x + b, y - a, color);
        tft180_draw_pixel(x - a, y + b, color);
        tft180_draw_pixel(x - b, y - a, color);
        tft180_draw_pixel(x - a, y - b, color);
        tft180_draw_pixel(x + b, y + a, color);
        tft180_draw_pixel(x + a, y - b, color);
        tft180_draw_pixel(x + a, y + b, color);
        tft180_draw_pixel(x - b, y + a, color);
        a++;

        if (d < 0) {
            d += 4 * a + 6;
        } else {
            d += 10 + 4 * (a - b);
            b--;
        }

        tft180_draw_pixel(x + a, y + b, color);
    }
}

/**
 * @brief 填充圆
 * @param x 圆心X坐标
 * @param y 圆心Y坐标
 * @param r 半径
 * @param color 颜色
 */
void tft180_fill_circle(uint8_t x, uint8_t y, uint8_t r, uint16_t color)
{
    int16_t a = 0, b = r;
    int16_t d = 3 - (r << 1);

    while (a <= b) {
        for (int16_t yi = x - b; yi <= x + b; yi++) {
            tft180_draw_pixel(yi, y - a, color);
            tft180_draw_pixel(yi, y + a, color);
        }

        for (int16_t yi = x - a; yi <= x + a; yi++) {
            tft180_draw_pixel(yi, y - b, color);
            tft180_draw_pixel(yi, y + b, color);
        }

        a++;
        if (d < 0) {
            d += 4 * a + 6;
        } else {
            d += 10 + 4 * (a - b);
            b--;
        }
    }
}

/**
 * @brief 显示字符
 * @param x X坐标
 * @param y Y坐标
 * @param ch 字符
 * @param color 前景色
 * @param bg_color 背景色
 */
void tft180_show_char(uint8_t x, uint8_t y, char ch, uint16_t color, uint16_t bg_color)
{
    if (x > TFT180_WIDTH - 6 || y > TFT180_HEIGHT - 8) return;

    uint8_t temp;
    uint8_t pos = ch - ' ';

    for (uint8_t i = 0; i < 6; i++) {
        temp = oled_font_6x8[pos][i];
        for (uint8_t j = 0; j < 8; j++) {
            if (temp & 0x80) {
                tft180_draw_pixel(x + i, y + j, color);
            } else {
                tft180_draw_pixel(x + i, y + j, bg_color);
            }
            temp <<= 1;
        }
    }
}

/**
 * @brief 显示字符串
 * @param x X坐标
 * @param y Y坐标
 * @param str 字符串
 * @param color 前景色
 * @param bg_color 背景色
 */
void tft180_show_string(uint8_t x, uint8_t y, const char *str, uint16_t color, uint16_t bg_color)
{
    uint8_t x0 = x;
    while (*str) {
        if (x > TFT180_WIDTH - 6) {
            x = x0;
            y += 8;
        }
        if (y > TFT180_HEIGHT - 8) break;

        tft180_show_char(x, y, *str, color, bg_color);
        x += 6;
        str++;
    }
}

/**
 * @brief 显示数字
 * @param x X坐标
 * @param y Y坐标
 * @param num 数字
 * @param len 显示长度
 * @param color 前景色
 * @param bg_color 背景色
 */
void tft180_show_num(uint8_t x, uint8_t y, uint32_t num, uint8_t len, uint16_t color, uint16_t bg_color)
{
    uint8_t t, temp;
    uint8_t enable_show = 0;

    for (t = 0; t < len; t++) {
        temp = (num / pow(10, len - t - 1)) % 10;
        if (enable_show == 0 && t < (len - 1)) {
            if (temp == 0) {
                tft180_show_char(x + 6 * t, y, ' ', color, bg_color);
                continue;
            } else {
                enable_show = 1;
            }
        }
        tft180_show_char(x + 6 * t, y, temp + '0', color, bg_color);
    }
}

/**
 * @brief 显示浮点数
 * @param x X坐标
 * @param y Y坐标
 * @param num 浮点数
 * @param integer_len 整数部分长度
 * @param color 前景色
 * @param bg_color 背景色
 */
void tft180_show_float(uint8_t x, uint8_t y, float num, uint8_t integer_len, uint16_t color, uint16_t bg_color)
{
    uint8_t t, temp;
    uint16_t num1;

    num1 = (int)num;
    tft180_show_num(x, y, num1, integer_len, color, bg_color);
    tft180_show_char(x + 6 * integer_len, y, '.', color, bg_color);

    num1 = (int)((num - (int)num) * 100);
    for (t = 0; t < 2; t++) {
        temp = (num1 / pow(10, 1 - t)) % 10;
        tft180_show_char(x + 6 * (integer_len + 1 + t), y, temp + '0', color, bg_color);
    }
}

/**
 * @brief 格式化输出
 * @param x X坐标
 * @param y Y坐标
 * @param color 前景色
 * @param bg_color 背景色
 * @param format 格式字符串
 * @return 输出字符数
 */
uint8_t tft180_printf(uint8_t x, uint8_t y, uint16_t color, uint16_t bg_color, const char *format, ...)
{
    char buffer[128];
    va_list args;

    va_start(args, format);
    uint8_t len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    tft180_show_string(x, y, buffer, color, bg_color);
    return len;
}
