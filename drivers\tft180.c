//
// TFT180 LCD Driver Implementation for MSPM0G3507
// Created for TFT180 1.8" 128x160 ST7735S LCD Display
//

#include "tft180.h"
#include <stdarg.h>
#include <stdio.h>
#include <math.h>

// 字体数据 (6x8像素字体)
extern const uint8_t oled_font_6x8[][6];

/**
 * @brief SPI发送一个字节
 * @param data 要发送的数据
 */
static void tft180_spi_write_byte(uint8_t data)
{
    // 等待SPI空闲
    while (DL_SPI_isBusy(SPI_TFT_INST));

    // 发送数据
    DL_SPI_transmitData8(SPI_TFT_INST, data);

    // 等待发送完成
    while (DL_SPI_isBusy(SPI_TFT_INST));
}

/**
 * @brief 写命令到TFT180
 * @param cmd 命令字节
 */
void tft180_write_cmd(uint8_t cmd)
{
    TFT_DC(0);  // 命令模式
    TFT_CS(0);  // 选中设备
    tft180_spi_write_byte(cmd);
    TFT_CS(1);  // 取消选中
}

/**
 * @brief 写数据到TFT180
 * @param data 数据字节
 */
void tft180_write_data(uint8_t data)
{
    TFT_DC(1);  // 数据模式
    TFT_CS(0);  // 选中设备
    tft180_spi_write_byte(data);
    TFT_CS(1);  // 取消选中
}

/**
 * @brief 写16位数据到TFT180
 * @param data 16位数据
 */
void tft180_write_data_16bit(uint16_t data)
{
    TFT_DC(1);  // 数据模式
    TFT_CS(0);  // 选中设备
    tft180_spi_write_byte(data >> 8);    // 高字节
    tft180_spi_write_byte(data & 0xFF);  // 低字节
    TFT_CS(1);  // 取消选中
}

/**
 * @brief 设置显示窗口
 * @param x0 起始X坐标
 * @param y0 起始Y坐标
 * @param x1 结束X坐标
 * @param y1 结束Y坐标
 */
void tft180_set_address_window(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1)
{
    // 设置列地址
    tft180_write_cmd(ST7735_CASET);
    tft180_write_data(0x00);
    tft180_write_data(x0 + TFT_XSTART);
    tft180_write_data(0x00);
    tft180_write_data(x1 + TFT_XSTART);

    // 设置行地址
    tft180_write_cmd(ST7735_RASET);
    tft180_write_data(0x00);
    tft180_write_data(y0 + TFT_YSTART);
    tft180_write_data(0x00);
    tft180_write_data(y1 + TFT_YSTART);

    // 开始写入RAM
    tft180_write_cmd(ST7735_RAMWR);
}

/**
 * @brief 初始化TFT180显示屏
 */
void tft180_init(void)
{
    // 初始化引脚状态
    TFT_CS(1);
    TFT_DC(1);
    TFT_BL(1);  // 打开背光

    // 硬件复位
    TFT_RES(0);
    delay_ms(10);
    TFT_RES(1);
    delay_ms(120);

    // 软件复位
    tft180_write_cmd(ST7735_SWRESET);
    delay_ms(150);

    // 退出睡眠模式
    tft180_write_cmd(ST7735_SLPOUT);
    delay_ms(500);

    // 帧率控制
    tft180_write_cmd(ST7735_FRMCTR1);
    tft180_write_data(0x01);
    tft180_write_data(0x2C);
    tft180_write_data(0x2D);

    tft180_write_cmd(ST7735_FRMCTR2);
    tft180_write_data(0x01);
    tft180_write_data(0x2C);
    tft180_write_data(0x2D);

    tft180_write_cmd(ST7735_FRMCTR3);
    tft180_write_data(0x01);
    tft180_write_data(0x2C);
    tft180_write_data(0x2D);
    tft180_write_data(0x01);
    tft180_write_data(0x2C);
    tft180_write_data(0x2D);

    // 显示反转控制
    tft180_write_cmd(ST7735_INVCTR);
    tft180_write_data(0x07);

    // 电源控制
    tft180_write_cmd(ST7735_PWCTR1);
    tft180_write_data(0xA2);
    tft180_write_data(0x02);
    tft180_write_data(0x84);

    tft180_write_cmd(ST7735_PWCTR2);
    tft180_write_data(0xC5);

    tft180_write_cmd(ST7735_PWCTR3);
    tft180_write_data(0x0A);
    tft180_write_data(0x00);

    tft180_write_cmd(ST7735_PWCTR4);
    tft180_write_data(0x8A);
    tft180_write_data(0x2A);

    tft180_write_cmd(ST7735_PWCTR5);
    tft180_write_data(0x8A);
    tft180_write_data(0xEE);

    // VCOM控制
    tft180_write_cmd(ST7735_VMCTR1);
    tft180_write_data(0x0E);

    // 关闭反色显示
    tft180_write_cmd(ST7735_INVOFF);

    // 内存访问控制
    tft180_write_cmd(ST7735_MADCTL);
    tft180_write_data(0xC8);

    // 颜色模式设置为16位
    tft180_write_cmd(ST7735_COLMOD);
    tft180_write_data(0x05);

    // Gamma校正
    tft180_write_cmd(ST7735_GMCTRP1);
    tft180_write_data(0x02);
    tft180_write_data(0x1C);
    tft180_write_data(0x07);
    tft180_write_data(0x12);
    tft180_write_data(0x37);
    tft180_write_data(0x32);
    tft180_write_data(0x29);
    tft180_write_data(0x2D);
    tft180_write_data(0x29);
    tft180_write_data(0x25);
    tft180_write_data(0x2B);
    tft180_write_data(0x39);
    tft180_write_data(0x00);
    tft180_write_data(0x01);
    tft180_write_data(0x03);
    tft180_write_data(0x10);

    tft180_write_cmd(ST7735_GMCTRN1);
    tft180_write_data(0x03);
    tft180_write_data(0x1D);
    tft180_write_data(0x07);
    tft180_write_data(0x06);
    tft180_write_data(0x2E);
    tft180_write_data(0x2C);
    tft180_write_data(0x29);
    tft180_write_data(0x2D);
    tft180_write_data(0x2E);
    tft180_write_data(0x2E);
    tft180_write_data(0x37);
    tft180_write_data(0x3F);
    tft180_write_data(0x00);
    tft180_write_data(0x00);
    tft180_write_data(0x02);
    tft180_write_data(0x10);

    // 正常显示模式
    tft180_write_cmd(ST7735_NORON);
    delay_ms(10);

    // 打开显示
    tft180_write_cmd(ST7735_DISPON);
    delay_ms(100);

    // 清屏
    tft180_fill_screen(TFT_BLACK);
}

/**
 * @brief 填充整个屏幕
 * @param color 填充颜色
 */
void tft180_fill_screen(uint16_t color)
{
    tft180_fill_rect(0, 0, TFT_WIDTH, TFT_HEIGHT, color);
}

/**
 * @brief 填充矩形区域
 * @param x X坐标
 * @param y Y坐标
 * @param w 宽度
 * @param h 高度
 * @param color 填充颜色
 */
void tft180_fill_rect(uint8_t x, uint8_t y, uint8_t w, uint8_t h, uint16_t color)
{
    if (x >= TFT_WIDTH || y >= TFT_HEIGHT) return;
    if (x + w > TFT_WIDTH) w = TFT_WIDTH - x;
    if (y + h > TFT_HEIGHT) h = TFT_HEIGHT - y;

    tft180_set_address_window(x, y, x + w - 1, y + h - 1);

    TFT_DC(1);  // 数据模式
    TFT_CS(0);  // 选中设备

    for (uint16_t i = 0; i < w * h; i++) {
        tft180_spi_write_byte(color >> 8);
        tft180_spi_write_byte(color & 0xFF);
    }

    TFT_CS(1);  // 取消选中
}

/**
 * @brief 画单个像素点
 * @param x X坐标
 * @param y Y坐标
 * @param color 像素颜色
 */
void tft180_draw_pixel(uint8_t x, uint8_t y, uint16_t color)
{
    if (x >= TFT_WIDTH || y >= TFT_HEIGHT) return;

    tft180_set_address_window(x, y, x, y);
    tft180_write_data_16bit(color);
}

/**
 * @brief 画直线
 * @param x0 起始X坐标
 * @param y0 起始Y坐标
 * @param x1 结束X坐标
 * @param y1 结束Y坐标
 * @param color 线条颜色
 */
void tft180_draw_line(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1, uint16_t color)
{
    int16_t dx = x1 - x0;
    int16_t dy = y1 - y0;
    int16_t ux = ((dx > 0) << 1) - 1;
    int16_t uy = ((dy > 0) << 1) - 1;
    int16_t x = x0, y = y0, eps;

    dx = dx > 0 ? dx : -dx;
    dy = dy > 0 ? dy : -dy;

    if (dx > dy) {
        eps = dx / 2;
        for (int16_t i = 0; i <= dx; i++) {
            tft180_draw_pixel(x, y, color);
            eps += dy;
            if ((eps << 1) >= dx) {
                y += uy;
                eps -= dx;
            }
            x += ux;
        }
    } else {
        eps = dy / 2;
        for (int16_t i = 0; i <= dy; i++) {
            tft180_draw_pixel(x, y, color);
            eps += dx;
            if ((eps << 1) >= dy) {
                x += ux;
                eps -= dy;
            }
            y += uy;
        }
    }
}

/**
 * @brief 画矩形框
 * @param x X坐标
 * @param y Y坐标
 * @param w 宽度
 * @param h 高度
 * @param color 线条颜色
 */
void tft180_draw_rect(uint8_t x, uint8_t y, uint8_t w, uint8_t h, uint16_t color)
{
    tft180_draw_line(x, y, x + w - 1, y, color);
    tft180_draw_line(x, y, x, y + h - 1, color);
    tft180_draw_line(x + w - 1, y, x + w - 1, y + h - 1, color);
    tft180_draw_line(x, y + h - 1, x + w - 1, y + h - 1, color);
}

/**
 * @brief 画圆
 * @param x0 圆心X坐标
 * @param y0 圆心Y坐标
 * @param r 半径
 * @param color 线条颜色
 */
void tft180_draw_circle(uint8_t x0, uint8_t y0, uint8_t r, uint16_t color)
{
    int16_t x = 0;
    int16_t y = r;
    int16_t d = 3 - (r << 1);

    while (x <= y) {
        tft180_draw_pixel(x0 + x, y0 + y, color);
        tft180_draw_pixel(x0 - x, y0 + y, color);
        tft180_draw_pixel(x0 + x, y0 - y, color);
        tft180_draw_pixel(x0 - x, y0 - y, color);
        tft180_draw_pixel(x0 + y, y0 + x, color);
        tft180_draw_pixel(x0 - y, y0 + x, color);
        tft180_draw_pixel(x0 + y, y0 - x, color);
        tft180_draw_pixel(x0 - y, y0 - x, color);

        if (d < 0) {
            d += (x << 2) + 6;
        } else {
            d += ((x - y) << 2) + 10;
            y--;
        }
        x++;
    }
}

/**
 * @brief 画实心圆
 * @param x0 圆心X坐标
 * @param y0 圆心Y坐标
 * @param r 半径
 * @param color 填充颜色
 */
void tft180_fill_circle(uint8_t x0, uint8_t y0, uint8_t r, uint16_t color)
{
    int16_t x = 0;
    int16_t y = r;
    int16_t d = 3 - (r << 1);

    while (x <= y) {
        tft180_draw_line(x0 - x, y0 + y, x0 + x, y0 + y, color);
        tft180_draw_line(x0 - x, y0 - y, x0 + x, y0 - y, color);
        tft180_draw_line(x0 - y, y0 + x, x0 + y, y0 + x, color);
        tft180_draw_line(x0 - y, y0 - x, x0 + y, y0 - x, color);

        if (d < 0) {
            d += (x << 2) + 6;
        } else {
            d += ((x - y) << 2) + 10;
            y--;
        }
        x++;
    }
}

/**
 * @brief 显示单个字符
 * @param x X坐标
 * @param y Y坐标
 * @param ch 字符
 * @param color 字符颜色
 * @param bg_color 背景颜色
 */
void tft180_show_char(uint8_t x, uint8_t y, char ch, uint16_t color, uint16_t bg_color)
{
    if (x > TFT_WIDTH - 6 || y > TFT_HEIGHT - 8) return;

    uint8_t temp;
    uint8_t pos = 0;

    ch = ch - ' ';  // 得到偏移后的值

    for (uint8_t t = 0; t < 6; t++) {
        temp = oled_font_6x8[ch][t];
        for (uint8_t t1 = 0; t1 < 8; t1++) {
            if (temp & 0x80) {
                tft180_draw_pixel(x + t, y + t1, color);
            } else {
                tft180_draw_pixel(x + t, y + t1, bg_color);
            }
            temp <<= 1;
        }
    }
}

/**
 * @brief 显示字符串
 * @param x X坐标
 * @param y Y坐标
 * @param str 字符串
 * @param color 字符颜色
 * @param bg_color 背景颜色
 */
void tft180_show_string(uint8_t x, uint8_t y, const char *str, uint16_t color, uint16_t bg_color)
{
    uint8_t x0 = x;

    while (*str != '\0') {
        if (x > TFT_WIDTH - 6) {
            x = x0;
            y += 8;
            if (y > TFT_HEIGHT - 8) break;
        }
        tft180_show_char(x, y, *str, color, bg_color);
        x += 6;
        str++;
    }
}

/**
 * @brief 显示数字
 * @param x X坐标
 * @param y Y坐标
 * @param num 数字
 * @param len 显示长度
 * @param color 字符颜色
 * @param bg_color 背景颜色
 */
void tft180_show_num(uint8_t x, uint8_t y, uint32_t num, uint8_t len, uint16_t color, uint16_t bg_color)
{
    uint8_t t, temp;
    uint8_t enshow = 0;

    for (t = 0; t < len; t++) {
        temp = (num / pow(10, len - t - 1)) % 10;
        if (enshow == 0 && t < (len - 1)) {
            if (temp == 0) {
                tft180_show_char(x + 6 * t, y, ' ', color, bg_color);
                continue;
            } else {
                enshow = 1;
            }
        }
        tft180_show_char(x + 6 * t, y, temp + '0', color, bg_color);
    }
}

/**
 * @brief 显示浮点数
 * @param x X坐标
 * @param y Y坐标
 * @param num 浮点数
 * @param integer_len 整数部分长度
 * @param decimal_len 小数部分长度
 * @param color 字符颜色
 * @param bg_color 背景颜色
 */
void tft180_show_float(uint8_t x, uint8_t y, float num, uint8_t integer_len, uint8_t decimal_len, uint16_t color, uint16_t bg_color)
{
    uint32_t integer_part = (uint32_t)num;
    uint32_t decimal_part = (uint32_t)((num - integer_part) * pow(10, decimal_len));

    tft180_show_num(x, y, integer_part, integer_len, color, bg_color);
    tft180_show_char(x + integer_len * 6, y, '.', color, bg_color);
    tft180_show_num(x + (integer_len + 1) * 6, y, decimal_part, decimal_len, color, bg_color);
}

/**
 * @brief 格式化显示字符串（类似printf）
 * @param x X坐标
 * @param y Y坐标
 * @param color 字符颜色
 * @param bg_color 背景颜色
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return 显示的字符数
 */
uint8_t tft180_printf(uint8_t x, uint8_t y, uint16_t color, uint16_t bg_color, const char *format, ...)
{
    char buffer[128];
    va_list args;

    va_start(args, format);
    uint8_t len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    tft180_show_string(x, y, buffer, color, bg_color);

    return len;
}