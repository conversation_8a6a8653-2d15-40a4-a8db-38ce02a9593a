<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty_LP_MSPM0G3507_nortos_ticlang.out -mempty_LP_MSPM0G3507_nortos_ticlang.map -iC:/ti/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang -iC:/Users/<USER>/workspace_ccstheia/empty_LP_MSPM0G3507_nortos_ticlang/Debug/syscfg -iD:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_LP_MSPM0G3507_nortos_ticlang_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./drivers/encoder.o ./drivers/flash.o ./drivers/imu.o ./drivers/key.o ./drivers/led.o ./drivers/motor.o ./drivers/nrf24l01.o ./drivers/oled.o ./drivers/oled_font.o ./drivers/openmv.o ./drivers/timer.o ./soft/debug.o ./soft/delay.o ./soft/menu.o ./soft/pid.o ./soft/protocol.o ./soft/task.o ./soft/vofa.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x6889d6fe</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x1ae1</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>encoder.o</file>
         <name>encoder.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>flash.o</file>
         <name>flash.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>imu.o</file>
         <name>imu.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>key.o</file>
         <name>key.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>led.o</file>
         <name>led.o</name>
      </input_file>
      <input_file id="fl-9">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>motor.o</file>
         <name>motor.o</name>
      </input_file>
      <input_file id="fl-a">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>nrf24l01.o</file>
         <name>nrf24l01.o</name>
      </input_file>
      <input_file id="fl-b">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>oled.o</file>
         <name>oled.o</name>
      </input_file>
      <input_file id="fl-c">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>oled_font.o</file>
         <name>oled_font.o</name>
      </input_file>
      <input_file id="fl-d">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>openmv.o</file>
         <name>openmv.o</name>
      </input_file>
      <input_file id="fl-e">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\drivers\</path>
         <kind>object</kind>
         <file>timer.o</file>
         <name>timer.o</name>
      </input_file>
      <input_file id="fl-f">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>debug.o</file>
         <name>debug.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>delay.o</file>
         <name>delay.o</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>menu.o</file>
         <name>menu.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>pid.o</file>
         <name>pid.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>protocol.o</file>
         <name>protocol.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>task.o</file>
         <name>task.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\soft\</path>
         <kind>object</kind>
         <file>vofa.o</file>
         <name>vofa.o</name>
      </input_file>
      <input_file id="fl-22">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-23">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-24">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_spi.o</name>
      </input_file>
      <input_file id="fl-25">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-26">
         <path>C:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-3d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>vsnprintf.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strlen.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-4e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-4f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-50">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-51">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-52">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-53">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-54">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-55">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-56">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-57">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-58">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-59">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-5a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-5b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-5c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-5d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-fd">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-fe">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-111">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-112">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-113">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-114">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-115">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-116">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-117">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-118">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-119">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-11a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-11b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-11c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-11d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-11e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-11f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-120">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-121">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-122">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-123">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-124">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-125">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-126">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-127">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-128">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
      <input_file id="fl-129">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcmp-armv6m.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.text.imu_analysis</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x214</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-70">
         <name>.text.encoder_exti_callback</name>
         <load_address>0x2d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d4</run_address>
         <size>0x20c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x4e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4e0</run_address>
         <size>0x1e0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.text.DL_Timer_initFourCCPWMMode</name>
         <load_address>0x6c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6c0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.text.UART3_IRQHandler</name>
         <load_address>0x7c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7c4</run_address>
         <size>0xf0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-198">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0x8b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8b4</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text.incremental_pid</name>
         <load_address>0x99c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x99c</run_address>
         <size>0xe4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text</name>
         <load_address>0xa80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa80</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-43">
         <name>.text.UART1_IRQHandler</name>
         <load_address>0xb58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb58</run_address>
         <size>0xc8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xc20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc20</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-120">
         <name>.text.nrf24l01_spi_read_write</name>
         <load_address>0xce4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xce4</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-75">
         <name>.text.nrf24l01_receive_callback</name>
         <load_address>0xd8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd8c</run_address>
         <size>0xa4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.text.left_motor_set_duty</name>
         <load_address>0xe30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe30</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.text.right_motor_set_duty</name>
         <load_address>0xec0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xec0</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.SYSCFG_DL_PWM_6_init</name>
         <load_address>0xf50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf50</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.SYSCFG_DL_PWM_7_init</name>
         <load_address>0xfdc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfdc</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.__mulsf3</name>
         <load_address>0x1068</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1068</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.text.timerA_callback</name>
         <load_address>0x10f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10f4</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-18c">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0x117c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x117c</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.text.__divsf3</name>
         <load_address>0x1200</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1200</run_address>
         <size>0x82</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-99">
         <name>.text.timerB_callback</name>
         <load_address>0x1282</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1282</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x1284</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1284</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-be">
         <name>.text.nrf24l01_read_buf</name>
         <load_address>0x1300</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1300</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.__aeabi_fcmp</name>
         <load_address>0x1364</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1364</run_address>
         <size>0x62</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x13c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13c8</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text.protocol_analysis</name>
         <load_address>0x1424</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1424</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.text.SYSCFG_DL_UART_1_init</name>
         <load_address>0x1480</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1480</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a6">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0x14d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14d4</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.nrf24l01_write_reg</name>
         <load_address>0x1520</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1520</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-1a4">
         <name>.text.DL_UART_init</name>
         <load_address>0x156c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x156c</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x15b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15b4</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.text.SYSCFG_DL_UART_3_init</name>
         <load_address>0x15fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15fc</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b5">
         <name>.text.DL_SPI_init</name>
         <load_address>0x1644</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1644</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text.encoder_callback</name>
         <load_address>0x1688</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1688</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.text.nrf24l01_read_reg</name>
         <load_address>0x16cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16cc</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-150">
         <name>.text.SYSCFG_DL_SPI_1_init</name>
         <load_address>0x1710</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1710</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.SYSCFG_DL_TIMER_8_init</name>
         <load_address>0x1750</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1750</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.text.DL_Timer_setCounterControl</name>
         <load_address>0x1790</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1790</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.SYSCFG_DL_TIMER_12_init</name>
         <load_address>0x17cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17cc</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text.__floatsisf</name>
         <load_address>0x1808</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1808</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.text.__gtsf2</name>
         <load_address>0x1844</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1844</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.text.get_verify_code</name>
         <load_address>0x1880</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1880</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x18bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18bc</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-137">
         <name>.text.__eqsf2</name>
         <load_address>0x18f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18f8</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-132">
         <name>.text.__muldsi3</name>
         <load_address>0x1934</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1934</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.__fixsfsi</name>
         <load_address>0x1970</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1970</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-183">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x19a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19a8</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-82">
         <name>.text.get_verify_code</name>
         <load_address>0x19dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19dc</run_address>
         <size>0x32</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-1b7">
         <name>.text.DL_SPI_setFIFOThreshold</name>
         <load_address>0x1a10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a10</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x1a40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a40</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1bb">
         <name>.text.DL_SYSTICK_init</name>
         <load_address>0x1a68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a68</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-48">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0x1a90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a90</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.motor_set_duty</name>
         <load_address>0x1ab8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ab8</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-69">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x1ae0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ae0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-1b6">
         <name>.text.DL_SPI_setBitRateSerialClockDivider</name>
         <load_address>0x1b08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b08</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x1b2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b2c</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-181">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x1b50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b50</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-83">
         <name>.text.openmv_analysis</name>
         <load_address>0x1b70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b70</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-1a5">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x1b90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b90</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x1bb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bb0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x1bcc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bcc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-187">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x1be8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1be8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-188">
         <name>.text.DL_GPIO_enableInterrupt</name>
         <load_address>0x1c04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c04</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x1c20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c20</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-185">
         <name>.text.DL_GPIO_setLowerPinsPolarity</name>
         <load_address>0x1c3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c3c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x1c58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c58</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-199">
         <name>.text.DL_Timer_enableInterrupt</name>
         <load_address>0x1c74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c74</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-191">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x1c90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c90</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x1cac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cac</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1a7">
         <name>.text.DL_UART_enableInterrupt</name>
         <load_address>0x1cc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1cc8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.text.TIMG12_IRQHandler</name>
         <load_address>0x1ce4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ce4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.TIMG8_IRQHandler</name>
         <load_address>0x1d00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d00</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.text.main</name>
         <load_address>0x1d1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d1c</run_address>
         <size>0x1a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-180">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x1d38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d38</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-177">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x1d50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d50</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.DL_GPIO_getEnabledInterruptStatus</name>
         <load_address>0x1d68</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d68</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text.DL_GPIO_getEnabledInterruptStatus</name>
         <load_address>0x1d80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d80</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-182">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x1d98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d98</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-173">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x1db0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1db0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x1dc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1dc8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-186">
         <name>.text.DL_GPIO_setUpperPinsPolarity</name>
         <load_address>0x1de0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1de0</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b8">
         <name>.text.DL_SPI_enable</name>
         <load_address>0x1df8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1df8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.text.DL_SPI_enablePower</name>
         <load_address>0x1e10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e10</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-176">
         <name>.text.DL_SPI_reset</name>
         <load_address>0x1e28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e28</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-189">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x1e40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e40</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-178">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x1e58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e58</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-174">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x1e70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e70</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-190">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x1e88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e88</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-179">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x1ea0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ea0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-175">
         <name>.text.DL_UART_reset</name>
         <load_address>0x1eb8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1eb8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1ed0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ed0</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-169">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1ee6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ee6</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-1a8">
         <name>.text.DL_UART_enable</name>
         <load_address>0x1efc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1efc</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-60">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x1f12</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f12</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1f28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f28</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-184">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1f3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f3c</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-18b">
         <name>.text.DL_SYSCTL_enableMFCLK</name>
         <load_address>0x1f50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f50</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-192">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x1f64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f64</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-193">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x1f78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f78</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x1f8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f8c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x1fa0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fa0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-81">
         <name>.text.DL_UART_receiveData</name>
         <load_address>0x1fb4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fb4</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-1af">
         <name>.text.DL_SPI_setClockConfig</name>
         <load_address>0x1fc8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fc8</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.text.DL_Timer_getPendingInterrupt</name>
         <load_address>0x1fda</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fda</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-94">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x1fec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fec</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x1ffe</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ffe</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.DL_UART_getPendingInterrupt</name>
         <load_address>0x2010</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2010</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x2022</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2022</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x2034</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2034</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x2046</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2046</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-1bc">
         <name>.text.DL_SYSTICK_enable</name>
         <load_address>0x2058</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2058</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16a">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x2068</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2068</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-151">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x2078</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2078</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x2088</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2088</run_address>
         <size>0xc</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x2094</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2094</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-168">
         <name>.text.nrf24l01_spi_delay</name>
         <load_address>0x209e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x209e</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-59">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x20a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20a8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text:abort</name>
         <load_address>0x20b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20b0</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x20b6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20b6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-158">
         <name>.text.HOSTexit</name>
         <load_address>0x20ba</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20ba</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x20be</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20be</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text._system_pre_init</name>
         <load_address>0x20c2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20c2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-211">
         <name>__TI_handler_table</name>
         <load_address>0x2140</load_address>
         <readonly>true</readonly>
         <run_address>0x2140</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-214">
         <name>.cinit..bss.load</name>
         <load_address>0x214c</load_address>
         <readonly>true</readonly>
         <run_address>0x214c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-213">
         <name>.cinit..data.load</name>
         <load_address>0x2154</load_address>
         <readonly>true</readonly>
         <run_address>0x2154</run_address>
         <size>0x8</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-212">
         <name>__TI_cinit_table</name>
         <load_address>0x215c</load_address>
         <readonly>true</readonly>
         <run_address>0x215c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-19d">
         <name>.rodata.gTIMER_12TimerConfig</name>
         <load_address>0x20c8</load_address>
         <readonly>true</readonly>
         <run_address>0x20c8</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.rodata.gTIMER_8TimerConfig</name>
         <load_address>0x20dc</load_address>
         <readonly>true</readonly>
         <run_address>0x20dc</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ba">
         <name>.rodata.gSPI_1_config</name>
         <load_address>0x20f0</load_address>
         <readonly>true</readonly>
         <run_address>0x20f0</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1aa">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x20fa</load_address>
         <readonly>true</readonly>
         <run_address>0x20fa</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ac">
         <name>.rodata.gUART_1Config</name>
         <load_address>0x2104</load_address>
         <readonly>true</readonly>
         <run_address>0x2104</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ae">
         <name>.rodata.gUART_3Config</name>
         <load_address>0x210e</load_address>
         <readonly>true</readonly>
         <run_address>0x210e</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-195">
         <name>.rodata.gPWM_6Config</name>
         <load_address>0x2118</load_address>
         <readonly>true</readonly>
         <run_address>0x2118</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-197">
         <name>.rodata.gPWM_7Config</name>
         <load_address>0x2120</load_address>
         <readonly>true</readonly>
         <run_address>0x2120</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-194">
         <name>.rodata.gPWM_6ClockConfig</name>
         <load_address>0x2128</load_address>
         <readonly>true</readonly>
         <run_address>0x2128</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-196">
         <name>.rodata.gPWM_7ClockConfig</name>
         <load_address>0x212b</load_address>
         <readonly>true</readonly>
         <run_address>0x212b</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.rodata.gTIMER_12ClockConfig</name>
         <load_address>0x212e</load_address>
         <readonly>true</readonly>
         <run_address>0x212e</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.rodata.gTIMER_8ClockConfig</name>
         <load_address>0x2131</load_address>
         <readonly>true</readonly>
         <run_address>0x2131</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1b9">
         <name>.rodata.gSPI_1_clockConfig</name>
         <load_address>0x2134</load_address>
         <readonly>true</readonly>
         <run_address>0x2134</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1a9">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x2136</load_address>
         <readonly>true</readonly>
         <run_address>0x2136</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ab">
         <name>.rodata.gUART_1ClockConfig</name>
         <load_address>0x2138</load_address>
         <readonly>true</readonly>
         <run_address>0x2138</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1ad">
         <name>.rodata.gUART_3ClockConfig</name>
         <load_address>0x213a</load_address>
         <readonly>true</readonly>
         <run_address>0x213a</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1db">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-b6">
         <name>.data.left_counter</name>
         <load_address>0x2020026c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020026c</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.data.right_counter</name>
         <load_address>0x20200270</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200270</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.data.left_speed</name>
         <load_address>0x2020026e</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020026e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-127">
         <name>.data.left_distance</name>
         <load_address>0x20200264</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200264</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-df">
         <name>.data.right_speed</name>
         <load_address>0x20200272</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200272</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-128">
         <name>.data.right_distance</name>
         <load_address>0x20200268</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200268</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.data.k</name>
         <load_address>0x20200260</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200260</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.data.imu_analysis.last_angle_z</name>
         <load_address>0x2020025c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020025c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.data.imu_analysis.angle_z</name>
         <load_address>0x20200258</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200258</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.data.state</name>
         <load_address>0x20200278</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200278</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-90">
         <name>.data.n</name>
         <load_address>0x20200275</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200275</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.data.receive_flag</name>
         <load_address>0x20200277</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200277</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.data.irq</name>
         <load_address>0x20200274</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200274</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-85">
         <name>.data.state</name>
         <load_address>0x20200279</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200279</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-86">
         <name>.data.n</name>
         <load_address>0x20200276</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200276</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-96">
         <name>.data.uart_data</name>
         <load_address>0x2020027a</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020027a</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-91">
         <name>.bss.buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200234</run_address>
         <size>0xb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-87">
         <name>.bss.rx_buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020023f</run_address>
         <size>0x8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-84">
         <name>.bss.data</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020024b</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.common:target_speed</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200254</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-de">
         <name>.common:left_speed_pid</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200170</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e0">
         <name>.common:right_speed_pid</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020019c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-152">
         <name>.common:gPWM_6Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-153">
         <name>.common:gPWM_7Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202000a0</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-154">
         <name>.common:gUART_3Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200140</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-155">
         <name>.common:gSPI_1Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001c8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f2">
         <name>.common:angle</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020021c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f4">
         <name>.common:gyroscope</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200228</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-f5">
         <name>.common:acceleration</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200210</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-8e">
         <name>.common:data</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200256</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-c5">
         <name>.common:receive_buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001f0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-e1">
         <name>.common:openmvData</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200247</run_address>
         <size>0x4</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-121">
         <name>.common:adcValue1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020024c</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-122">
         <name>.common:medAdcValue1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200250</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-123">
         <name>.common:adcValue2</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020024e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-124">
         <name>.common:medAdcValue2</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200252</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-216">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20203e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-156">
         <name>.debug_abbrev</name>
         <load_address>0xd2</load_address>
         <run_address>0xd2</run_address>
         <size>0x210</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_abbrev</name>
         <load_address>0x2e2</load_address>
         <run_address>0x2e2</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_abbrev</name>
         <load_address>0x34f</load_address>
         <run_address>0x34f</run_address>
         <size>0x185</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_abbrev</name>
         <load_address>0x4d4</load_address>
         <run_address>0x4d4</run_address>
         <size>0x1b3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_abbrev</name>
         <load_address>0x687</load_address>
         <run_address>0x687</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_abbrev</name>
         <load_address>0x77f</load_address>
         <run_address>0x77f</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_abbrev</name>
         <load_address>0x934</load_address>
         <run_address>0x934</run_address>
         <size>0x1da</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_abbrev</name>
         <load_address>0xb0e</load_address>
         <run_address>0xb0e</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_abbrev</name>
         <load_address>0xc73</load_address>
         <run_address>0xc73</run_address>
         <size>0x1c4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_abbrev</name>
         <load_address>0xe37</load_address>
         <run_address>0xe37</run_address>
         <size>0x9d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-125">
         <name>.debug_abbrev</name>
         <load_address>0xed4</load_address>
         <run_address>0xed4</run_address>
         <size>0x53</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-1ca">
         <name>.debug_abbrev</name>
         <load_address>0xf27</load_address>
         <run_address>0xf27</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1d0">
         <name>.debug_abbrev</name>
         <load_address>0xf89</load_address>
         <run_address>0xf89</run_address>
         <size>0x277</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1c8">
         <name>.debug_abbrev</name>
         <load_address>0x1200</load_address>
         <run_address>0x1200</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.debug_abbrev</name>
         <load_address>0x1486</load_address>
         <run_address>0x1486</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_abbrev</name>
         <load_address>0x1721</load_address>
         <run_address>0x1721</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-162">
         <name>.debug_abbrev</name>
         <load_address>0x17d0</load_address>
         <run_address>0x17d0</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_abbrev</name>
         <load_address>0x1940</load_address>
         <run_address>0x1940</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_abbrev</name>
         <load_address>0x1979</load_address>
         <run_address>0x1979</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_abbrev</name>
         <load_address>0x1a3b</load_address>
         <run_address>0x1a3b</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_abbrev</name>
         <load_address>0x1aab</load_address>
         <run_address>0x1aab</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.debug_abbrev</name>
         <load_address>0x1b38</load_address>
         <run_address>0x1b38</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-1bd">
         <name>.debug_abbrev</name>
         <load_address>0x1bd0</load_address>
         <run_address>0x1bd0</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.debug_abbrev</name>
         <load_address>0x1bfc</load_address>
         <run_address>0x1bfc</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-170">
         <name>.debug_abbrev</name>
         <load_address>0x1c23</load_address>
         <run_address>0x1c23</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_abbrev</name>
         <load_address>0x1c4a</load_address>
         <run_address>0x1c4a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_abbrev</name>
         <load_address>0x1c71</load_address>
         <run_address>0x1c71</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_abbrev</name>
         <load_address>0x1c98</load_address>
         <run_address>0x1c98</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_abbrev</name>
         <load_address>0x1cbf</load_address>
         <run_address>0x1cbf</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.debug_abbrev</name>
         <load_address>0x1ce6</load_address>
         <run_address>0x1ce6</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_abbrev</name>
         <load_address>0x1d0d</load_address>
         <run_address>0x1d0d</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-171">
         <name>.debug_abbrev</name>
         <load_address>0x1d34</load_address>
         <run_address>0x1d34</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.debug_abbrev</name>
         <load_address>0x1d59</load_address>
         <run_address>0x1d59</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
      <object_component id="oc-218">
         <name>.debug_abbrev</name>
         <load_address>0x1d7e</load_address>
         <run_address>0x1d7e</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1dd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_info</name>
         <load_address>0x1dd</load_address>
         <run_address>0x1dd</run_address>
         <size>0x39d1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x3bae</load_address>
         <run_address>0x3bae</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_info</name>
         <load_address>0x3c2e</load_address>
         <run_address>0x3c2e</run_address>
         <size>0xb04</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_info</name>
         <load_address>0x4732</load_address>
         <run_address>0x4732</run_address>
         <size>0x9d4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_info</name>
         <load_address>0x5106</load_address>
         <run_address>0x5106</run_address>
         <size>0x7f3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_info</name>
         <load_address>0x58f9</load_address>
         <run_address>0x58f9</run_address>
         <size>0xeeb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_info</name>
         <load_address>0x67e4</load_address>
         <run_address>0x67e4</run_address>
         <size>0xa45</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_info</name>
         <load_address>0x7229</load_address>
         <run_address>0x7229</run_address>
         <size>0xa13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_info</name>
         <load_address>0x7c3c</load_address>
         <run_address>0x7c3c</run_address>
         <size>0xa81</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_info</name>
         <load_address>0x86bd</load_address>
         <run_address>0x86bd</run_address>
         <size>0x2b0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_info</name>
         <load_address>0x896d</load_address>
         <run_address>0x896d</run_address>
         <size>0x9f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.debug_info</name>
         <load_address>0x8a0c</load_address>
         <run_address>0x8a0c</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b1">
         <name>.debug_info</name>
         <load_address>0x8a81</load_address>
         <run_address>0x8a81</run_address>
         <size>0x1142</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.debug_info</name>
         <load_address>0x9bc3</load_address>
         <run_address>0x9bc3</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1a0">
         <name>.debug_info</name>
         <load_address>0xcd35</load_address>
         <run_address>0xcd35</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xdfdb</load_address>
         <run_address>0xdfdb</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_info</name>
         <load_address>0xe3fe</load_address>
         <run_address>0xe3fe</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_info</name>
         <load_address>0xeb42</load_address>
         <run_address>0xeb42</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_info</name>
         <load_address>0xeb88</load_address>
         <run_address>0xeb88</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0xed1a</load_address>
         <run_address>0xed1a</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0xede0</load_address>
         <run_address>0xede0</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_info</name>
         <load_address>0xef5c</load_address>
         <run_address>0xef5c</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.debug_info</name>
         <load_address>0xf054</load_address>
         <run_address>0xf054</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_info</name>
         <load_address>0xf08f</load_address>
         <run_address>0xf08f</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_info</name>
         <load_address>0xf236</load_address>
         <run_address>0xf236</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_info</name>
         <load_address>0xf3c5</load_address>
         <run_address>0xf3c5</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_info</name>
         <load_address>0xf552</load_address>
         <run_address>0xf552</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_info</name>
         <load_address>0xf6df</load_address>
         <run_address>0xf6df</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_info</name>
         <load_address>0xf86e</load_address>
         <run_address>0xf86e</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_info</name>
         <load_address>0xfa01</load_address>
         <run_address>0xfa01</run_address>
         <size>0x217</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_info</name>
         <load_address>0xfc18</load_address>
         <run_address>0xfc18</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_info</name>
         <load_address>0xfdb1</load_address>
         <run_address>0xfdb1</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_info</name>
         <load_address>0xff72</load_address>
         <run_address>0xff72</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
      <object_component id="oc-217">
         <name>.debug_info</name>
         <load_address>0x1026c</load_address>
         <run_address>0x1026c</run_address>
         <size>0xbc</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_ranges</name>
         <load_address>0x28</load_address>
         <run_address>0x28</run_address>
         <size>0x1a0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_ranges</name>
         <load_address>0x1c8</load_address>
         <run_address>0x1c8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_ranges</name>
         <load_address>0x1e0</load_address>
         <run_address>0x1e0</run_address>
         <size>0x40</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x220</load_address>
         <run_address>0x220</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_ranges</name>
         <load_address>0x268</load_address>
         <run_address>0x268</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_ranges</name>
         <load_address>0x298</load_address>
         <run_address>0x298</run_address>
         <size>0xa8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x340</load_address>
         <run_address>0x340</run_address>
         <size>0x60</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_ranges</name>
         <load_address>0x3a0</load_address>
         <run_address>0x3a0</run_address>
         <size>0x40</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_ranges</name>
         <load_address>0x3e0</load_address>
         <run_address>0x3e0</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_ranges</name>
         <load_address>0x450</load_address>
         <run_address>0x450</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-1b2">
         <name>.debug_ranges</name>
         <load_address>0x478</load_address>
         <run_address>0x478</run_address>
         <size>0x390</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.debug_ranges</name>
         <load_address>0x808</load_address>
         <run_address>0x808</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.debug_ranges</name>
         <load_address>0x9e0</load_address>
         <run_address>0x9e0</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_ranges</name>
         <load_address>0xb88</load_address>
         <run_address>0xb88</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_ranges</name>
         <load_address>0xbd0</load_address>
         <run_address>0xbd0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_ranges</name>
         <load_address>0xc18</load_address>
         <run_address>0xc18</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_ranges</name>
         <load_address>0xc30</load_address>
         <run_address>0xc30</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_ranges</name>
         <load_address>0xc80</load_address>
         <run_address>0xc80</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_ranges</name>
         <load_address>0xc98</load_address>
         <run_address>0xc98</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_ranges</name>
         <load_address>0xcd0</load_address>
         <run_address>0xcd0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x23c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-157">
         <name>.debug_str</name>
         <load_address>0x23c</load_address>
         <run_address>0x23c</run_address>
         <size>0x300a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_str</name>
         <load_address>0x3246</load_address>
         <run_address>0x3246</run_address>
         <size>0x173</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_str</name>
         <load_address>0x33b9</load_address>
         <run_address>0x33b9</run_address>
         <size>0x7de</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_str</name>
         <load_address>0x3b97</load_address>
         <run_address>0x3b97</run_address>
         <size>0x871</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_str</name>
         <load_address>0x4408</load_address>
         <run_address>0x4408</run_address>
         <size>0x4ba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_str</name>
         <load_address>0x48c2</load_address>
         <run_address>0x48c2</run_address>
         <size>0x6ba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_str</name>
         <load_address>0x4f7c</load_address>
         <run_address>0x4f7c</run_address>
         <size>0x8a4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_str</name>
         <load_address>0x5820</load_address>
         <run_address>0x5820</run_address>
         <size>0x87c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_str</name>
         <load_address>0x609c</load_address>
         <run_address>0x609c</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_str</name>
         <load_address>0x694b</load_address>
         <run_address>0x694b</run_address>
         <size>0x1f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-126">
         <name>.debug_str</name>
         <load_address>0x6b44</load_address>
         <run_address>0x6b44</run_address>
         <size>0x137</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-1cb">
         <name>.debug_str</name>
         <load_address>0x6c7b</load_address>
         <run_address>0x6c7b</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1d1">
         <name>.debug_str</name>
         <load_address>0x6df3</load_address>
         <run_address>0x6df3</run_address>
         <size>0xc46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-1c9">
         <name>.debug_str</name>
         <load_address>0x7a39</load_address>
         <run_address>0x7a39</run_address>
         <size>0x1dd7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1cf">
         <name>.debug_str</name>
         <load_address>0x9810</load_address>
         <run_address>0x9810</run_address>
         <size>0xcee</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_str</name>
         <load_address>0xa4fe</load_address>
         <run_address>0xa4fe</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-163">
         <name>.debug_str</name>
         <load_address>0xa723</load_address>
         <run_address>0xa723</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_str</name>
         <load_address>0xaa52</load_address>
         <run_address>0xaa52</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_str</name>
         <load_address>0xab47</load_address>
         <run_address>0xab47</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_str</name>
         <load_address>0xace2</load_address>
         <run_address>0xace2</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_str</name>
         <load_address>0xae4a</load_address>
         <run_address>0xae4a</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_str</name>
         <load_address>0xb01f</load_address>
         <run_address>0xb01f</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-1be">
         <name>.debug_str</name>
         <load_address>0xb167</load_address>
         <run_address>0xb167</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_frame</name>
         <load_address>0x70</load_address>
         <run_address>0x70</run_address>
         <size>0x4b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_frame</name>
         <load_address>0x528</load_address>
         <run_address>0x528</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_frame</name>
         <load_address>0x558</load_address>
         <run_address>0x558</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_frame</name>
         <load_address>0x600</load_address>
         <run_address>0x600</run_address>
         <size>0xc8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_frame</name>
         <load_address>0x6c8</load_address>
         <run_address>0x6c8</run_address>
         <size>0x9c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_frame</name>
         <load_address>0x764</load_address>
         <run_address>0x764</run_address>
         <size>0x208</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_frame</name>
         <load_address>0x96c</load_address>
         <run_address>0x96c</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_frame</name>
         <load_address>0xa6c</load_address>
         <run_address>0xa6c</run_address>
         <size>0xb4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_frame</name>
         <load_address>0xb20</load_address>
         <run_address>0xb20</run_address>
         <size>0x140</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_frame</name>
         <load_address>0xc60</load_address>
         <run_address>0xc60</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_frame</name>
         <load_address>0xce0</load_address>
         <run_address>0xce0</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.debug_frame</name>
         <load_address>0xd04</load_address>
         <run_address>0xd04</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b3">
         <name>.debug_frame</name>
         <load_address>0xd24</load_address>
         <run_address>0xd24</run_address>
         <size>0x234</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.debug_frame</name>
         <load_address>0xf58</load_address>
         <run_address>0xf58</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.debug_frame</name>
         <load_address>0x1360</load_address>
         <run_address>0x1360</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_frame</name>
         <load_address>0x1518</load_address>
         <run_address>0x1518</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_frame</name>
         <load_address>0x15a8</load_address>
         <run_address>0x15a8</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_frame</name>
         <load_address>0x16a8</load_address>
         <run_address>0x16a8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_frame</name>
         <load_address>0x16c8</load_address>
         <run_address>0x16c8</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0x1700</load_address>
         <run_address>0x1700</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x1728</load_address>
         <run_address>0x1728</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_frame</name>
         <load_address>0x1758</load_address>
         <run_address>0x1758</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.debug_frame</name>
         <load_address>0x1788</load_address>
         <run_address>0x1788</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_line</name>
         <load_address>0x1c6</load_address>
         <run_address>0x1c6</run_address>
         <size>0xd1e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0xee4</load_address>
         <run_address>0xee4</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_line</name>
         <load_address>0xf9c</load_address>
         <run_address>0xf9c</run_address>
         <size>0x48b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_line</name>
         <load_address>0x1427</load_address>
         <run_address>0x1427</run_address>
         <size>0x551</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_line</name>
         <load_address>0x1978</load_address>
         <run_address>0x1978</run_address>
         <size>0x2f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-9"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_line</name>
         <load_address>0x1c71</load_address>
         <run_address>0x1c71</run_address>
         <size>0x8e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-a"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_line</name>
         <load_address>0x2558</load_address>
         <run_address>0x2558</run_address>
         <size>0x4db</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_line</name>
         <load_address>0x2a33</load_address>
         <run_address>0x2a33</run_address>
         <size>0x318</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_line</name>
         <load_address>0x2d4b</load_address>
         <run_address>0x2d4b</run_address>
         <size>0x493</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_line</name>
         <load_address>0x31de</load_address>
         <run_address>0x31de</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_line</name>
         <load_address>0x3479</load_address>
         <run_address>0x3479</run_address>
         <size>0x145</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.debug_line</name>
         <load_address>0x35be</load_address>
         <run_address>0x35be</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b0">
         <name>.debug_line</name>
         <load_address>0x3737</load_address>
         <run_address>0x3737</run_address>
         <size>0xc1b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_line</name>
         <load_address>0x4352</load_address>
         <run_address>0x4352</run_address>
         <size>0x176f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1a3">
         <name>.debug_line</name>
         <load_address>0x5ac1</load_address>
         <run_address>0x5ac1</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_line</name>
         <load_address>0x64d9</load_address>
         <run_address>0x64d9</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_line</name>
         <load_address>0x66b5</load_address>
         <run_address>0x66b5</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_line</name>
         <load_address>0x6bcf</load_address>
         <run_address>0x6bcf</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_line</name>
         <load_address>0x6c0d</load_address>
         <run_address>0x6c0d</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x6d0b</load_address>
         <run_address>0x6d0b</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x6dcb</load_address>
         <run_address>0x6dcb</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_line</name>
         <load_address>0x6f93</load_address>
         <run_address>0x6f93</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-159">
         <name>.debug_line</name>
         <load_address>0x6ffa</load_address>
         <run_address>0x6ffa</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_line</name>
         <load_address>0x703b</load_address>
         <run_address>0x703b</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_line</name>
         <load_address>0x7142</load_address>
         <run_address>0x7142</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_line</name>
         <load_address>0x71fb</load_address>
         <run_address>0x71fb</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_line</name>
         <load_address>0x72db</load_address>
         <run_address>0x72db</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_line</name>
         <load_address>0x73b7</load_address>
         <run_address>0x73b7</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_line</name>
         <load_address>0x746f</load_address>
         <run_address>0x746f</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_line</name>
         <load_address>0x752b</load_address>
         <run_address>0x752b</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_line</name>
         <load_address>0x75f2</load_address>
         <run_address>0x75f2</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.debug_line</name>
         <load_address>0x7696</load_address>
         <run_address>0x7696</run_address>
         <size>0x104</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_line</name>
         <load_address>0x779a</load_address>
         <run_address>0x779a</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
      <object_component id="oc-1cc">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-23"/>
      </object_component>
      <object_component id="oc-1b4">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x816</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-16b">
         <name>.debug_loc</name>
         <load_address>0x829</load_address>
         <run_address>0x829</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.debug_loc</name>
         <load_address>0x2250</load_address>
         <run_address>0x2250</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-26"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_loc</name>
         <load_address>0x2a0c</load_address>
         <run_address>0x2a0c</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.debug_loc</name>
         <load_address>0x2ae4</load_address>
         <run_address>0x2ae4</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_loc</name>
         <load_address>0x2f08</load_address>
         <run_address>0x2f08</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_loc</name>
         <load_address>0x3074</load_address>
         <run_address>0x3074</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-45"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_loc</name>
         <load_address>0x30e3</load_address>
         <run_address>0x30e3</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-46"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_loc</name>
         <load_address>0x324a</load_address>
         <run_address>0x324a</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4c"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10d"/>
      </object_component>
      <object_component id="oc-133">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-110"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-112"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-115"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-117"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11b"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.debug_aranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11d"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.debug_aranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-121"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_aranges</name>
         <load_address>0x130</load_address>
         <run_address>0x130</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-127"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x2008</size>
         <contents>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-18c"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-1a6"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-1a4"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-1b5"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-1b7"/>
            <object_component_ref idref="oc-1cd"/>
            <object_component_ref idref="oc-1bb"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-1b6"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-1a5"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-1a7"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-1b8"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-1a8"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-1af"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-1bc"/>
            <object_component_ref idref="oc-16a"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-a3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x2140</load_address>
         <run_address>0x2140</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-211"/>
            <object_component_ref idref="oc-214"/>
            <object_component_ref idref="oc-213"/>
            <object_component_ref idref="oc-212"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x20c8</load_address>
         <run_address>0x20c8</run_address>
         <size>0x78</size>
         <contents>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-1ba"/>
            <object_component_ref idref="oc-1aa"/>
            <object_component_ref idref="oc-1ac"/>
            <object_component_ref idref="oc-1ae"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-19c"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-1b9"/>
            <object_component_ref idref="oc-1a9"/>
            <object_component_ref idref="oc-1ab"/>
            <object_component_ref idref="oc-1ad"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-1db"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200258</run_address>
         <size>0x23</size>
         <contents>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-96"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x257</size>
         <contents>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-124"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-216"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d2" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d3" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d4" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d5" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d6" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d7" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d9" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1f5" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1d8d</size>
         <contents>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-1ca"/>
            <object_component_ref idref="oc-1d0"/>
            <object_component_ref idref="oc-1c8"/>
            <object_component_ref idref="oc-1ce"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-1bd"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-218"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f7" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10328</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-1b1"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-217"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1f9" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xcf8</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-1b2"/>
            <object_component_ref idref="oc-16c"/>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-f9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1fb" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb250</size>
         <contents>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-1cb"/>
            <object_component_ref idref="oc-1d1"/>
            <object_component_ref idref="oc-1c9"/>
            <object_component_ref idref="oc-1cf"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-1be"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1fd" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x17a8</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-1b3"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-15a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ff" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x783a</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-1b0"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-1a3"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-fa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-201" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3270</size>
         <contents>
            <object_component_ref idref="oc-1cc"/>
            <object_component_ref idref="oc-1b4"/>
            <object_component_ref idref="oc-16b"/>
            <object_component_ref idref="oc-1a1"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-10d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-20b" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x158</size>
         <contents>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-fb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-215" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-223" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2170</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-224" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x27b</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-225" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20203e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x8000</length>
         <used_space>0x2170</used_space>
         <unused_space>0x5e90</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x2008</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20c8</start_address>
               <size>0x78</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2140</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x2170</start_address>
               <size>0x5e90</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x4000</length>
         <used_space>0x47a</used_space>
         <unused_space>0x3b86</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1d7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-1d9"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x257</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200257</start_address>
               <size>0x1</size>
            </available_space>
            <allocated_space>
               <start_address>0x20200258</start_address>
               <size>0x23</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x2020027b</start_address>
               <size>0x3b85</size>
            </available_space>
            <allocated_space>
               <start_address>0x20203e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x214c</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x257</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0x2154</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200258</run_address>
            <run_size>0x23</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x215c</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x216c</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x216c</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x2140</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x214c</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20204000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-42">
         <name>main</name>
         <value>0x1d1d</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-43">
         <name>timerA_callback</name>
         <value>0x10f5</value>
         <object_component_ref idref="oc-7d"/>
      </symbol>
      <symbol id="sm-44">
         <name>target_speed</name>
         <value>0x20200254</value>
      </symbol>
      <symbol id="sm-45">
         <name>left_speed_pid</name>
         <value>0x20200170</value>
      </symbol>
      <symbol id="sm-46">
         <name>right_speed_pid</name>
         <value>0x2020019c</value>
      </symbol>
      <symbol id="sm-47">
         <name>timerB_callback</name>
         <value>0x1283</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-48">
         <name>GROUP1_IRQHandler</name>
         <value>0x2089</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-11e">
         <name>SYSCFG_DL_init</name>
         <value>0x13c9</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-11f">
         <name>SYSCFG_DL_initPower</name>
         <value>0xc21</value>
         <object_component_ref idref="oc-146"/>
      </symbol>
      <symbol id="sm-120">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x4e1</value>
         <object_component_ref idref="oc-147"/>
      </symbol>
      <symbol id="sm-121">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x1b2d</value>
         <object_component_ref idref="oc-148"/>
      </symbol>
      <symbol id="sm-122">
         <name>SYSCFG_DL_PWM_6_init</name>
         <value>0xf51</value>
         <object_component_ref idref="oc-149"/>
      </symbol>
      <symbol id="sm-123">
         <name>SYSCFG_DL_PWM_7_init</name>
         <value>0xfdd</value>
         <object_component_ref idref="oc-14a"/>
      </symbol>
      <symbol id="sm-124">
         <name>SYSCFG_DL_TIMER_8_init</name>
         <value>0x1751</value>
         <object_component_ref idref="oc-14b"/>
      </symbol>
      <symbol id="sm-125">
         <name>SYSCFG_DL_TIMER_12_init</name>
         <value>0x17cd</value>
         <object_component_ref idref="oc-14c"/>
      </symbol>
      <symbol id="sm-126">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x15b5</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-127">
         <name>SYSCFG_DL_UART_1_init</name>
         <value>0x1481</value>
         <object_component_ref idref="oc-14e"/>
      </symbol>
      <symbol id="sm-128">
         <name>SYSCFG_DL_UART_3_init</name>
         <value>0x15fd</value>
         <object_component_ref idref="oc-14f"/>
      </symbol>
      <symbol id="sm-129">
         <name>SYSCFG_DL_SPI_1_init</name>
         <value>0x1711</value>
         <object_component_ref idref="oc-150"/>
      </symbol>
      <symbol id="sm-12a">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x2079</value>
         <object_component_ref idref="oc-151"/>
      </symbol>
      <symbol id="sm-12b">
         <name>gPWM_6Backup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-12c">
         <name>gPWM_7Backup</name>
         <value>0x202000a0</value>
      </symbol>
      <symbol id="sm-12d">
         <name>gUART_3Backup</name>
         <value>0x20200140</value>
      </symbol>
      <symbol id="sm-12e">
         <name>gSPI_1Backup</name>
         <value>0x202001c8</value>
      </symbol>
      <symbol id="sm-139">
         <name>Default_Handler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13a">
         <name>Reset_Handler</name>
         <value>0x20bf</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-13b">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-13c">
         <name>NMI_Handler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13d">
         <name>HardFault_Handler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13e">
         <name>SVC_Handler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13f">
         <name>PendSV_Handler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-140">
         <name>SysTick_Handler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-141">
         <name>GROUP0_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-142">
         <name>ADC0_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-143">
         <name>ADC1_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-144">
         <name>CANFD0_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-145">
         <name>DAC0_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-146">
         <name>SPI0_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-147">
         <name>SPI1_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-148">
         <name>UART2_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-149">
         <name>TIMG0_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14a">
         <name>TIMG6_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14b">
         <name>TIMA0_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14c">
         <name>TIMA1_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14d">
         <name>TIMG7_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14e">
         <name>I2C0_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14f">
         <name>I2C1_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-150">
         <name>AES_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-151">
         <name>RTC_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-152">
         <name>DMA_IRQHandler</name>
         <value>0x20b7</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-16a">
         <name>encoder_callback</name>
         <value>0x1689</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-16b">
         <name>left_counter</name>
         <value>0x2020026c</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-16c">
         <name>left_speed</name>
         <value>0x2020026e</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-16d">
         <name>left_distance</name>
         <value>0x20200264</value>
         <object_component_ref idref="oc-127"/>
      </symbol>
      <symbol id="sm-16e">
         <name>right_counter</name>
         <value>0x20200270</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-16f">
         <name>right_speed</name>
         <value>0x20200272</value>
         <object_component_ref idref="oc-df"/>
      </symbol>
      <symbol id="sm-170">
         <name>right_distance</name>
         <value>0x20200268</value>
         <object_component_ref idref="oc-128"/>
      </symbol>
      <symbol id="sm-171">
         <name>encoder_exti_callback</name>
         <value>0x2d5</value>
         <object_component_ref idref="oc-70"/>
      </symbol>
      <symbol id="sm-194">
         <name>imu_analysis</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-8d"/>
      </symbol>
      <symbol id="sm-195">
         <name>angle</name>
         <value>0x2020021c</value>
      </symbol>
      <symbol id="sm-196">
         <name>gyroscope</name>
         <value>0x20200228</value>
      </symbol>
      <symbol id="sm-197">
         <name>acceleration</name>
         <value>0x20200210</value>
      </symbol>
      <symbol id="sm-198">
         <name>k</name>
         <value>0x20200260</value>
         <object_component_ref idref="oc-f7"/>
      </symbol>
      <symbol id="sm-199">
         <name>UART1_IRQHandler</name>
         <value>0xb59</value>
         <object_component_ref idref="oc-43"/>
      </symbol>
      <symbol id="sm-19a">
         <name>data</name>
         <value>0x20200256</value>
      </symbol>
      <symbol id="sm-1ab">
         <name>left_motor_set_duty</name>
         <value>0xe31</value>
         <object_component_ref idref="oc-12d"/>
      </symbol>
      <symbol id="sm-1ac">
         <name>right_motor_set_duty</name>
         <value>0xec1</value>
         <object_component_ref idref="oc-12e"/>
      </symbol>
      <symbol id="sm-1ad">
         <name>motor_set_duty</name>
         <value>0x1ab9</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-1db">
         <name>nrf24l01_spi_read_write</name>
         <value>0xce5</value>
         <object_component_ref idref="oc-120"/>
      </symbol>
      <symbol id="sm-1dc">
         <name>nrf24l01_receive_callback</name>
         <value>0xd8d</value>
         <object_component_ref idref="oc-75"/>
      </symbol>
      <symbol id="sm-1dd">
         <name>irq</name>
         <value>0x20200274</value>
         <object_component_ref idref="oc-c4"/>
      </symbol>
      <symbol id="sm-1de">
         <name>receive_buffer</name>
         <value>0x202001f0</value>
      </symbol>
      <symbol id="sm-1df">
         <name>receive_flag</name>
         <value>0x20200277</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-200">
         <name>openmv_analysis</name>
         <value>0x1b71</value>
         <object_component_ref idref="oc-83"/>
      </symbol>
      <symbol id="sm-201">
         <name>openmvData</name>
         <value>0x20200247</value>
      </symbol>
      <symbol id="sm-202">
         <name>UART3_IRQHandler</name>
         <value>0x7c5</value>
         <object_component_ref idref="oc-3e"/>
      </symbol>
      <symbol id="sm-212">
         <name>TIMG8_IRQHandler</name>
         <value>0x1d01</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-213">
         <name>TIMG12_IRQHandler</name>
         <value>0x1ce5</value>
         <object_component_ref idref="oc-4d"/>
      </symbol>
      <symbol id="sm-224">
         <name>UART0_IRQHandler</name>
         <value>0x1a91</value>
         <object_component_ref idref="oc-48"/>
      </symbol>
      <symbol id="sm-225">
         <name>uart_data</name>
         <value>0x2020027a</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-230">
         <name>incremental_pid</name>
         <value>0x99d</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-239">
         <name>protocol_analysis</name>
         <value>0x1425</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-23a">
         <name>adcValue1</name>
         <value>0x2020024c</value>
      </symbol>
      <symbol id="sm-23b">
         <name>medAdcValue1</name>
         <value>0x20200250</value>
      </symbol>
      <symbol id="sm-23c">
         <name>adcValue2</name>
         <value>0x2020024e</value>
      </symbol>
      <symbol id="sm-23d">
         <name>medAdcValue2</name>
         <value>0x20200252</value>
      </symbol>
      <symbol id="sm-240">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-241">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-242">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-243">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-244">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-245">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-246">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-247">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-248">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-251">
         <name>DL_Common_delayCycles</name>
         <value>0x2095</value>
         <object_component_ref idref="oc-17b"/>
      </symbol>
      <symbol id="sm-25e">
         <name>DL_SPI_init</name>
         <value>0x1645</value>
         <object_component_ref idref="oc-1b5"/>
      </symbol>
      <symbol id="sm-25f">
         <name>DL_SPI_setClockConfig</name>
         <value>0x1fc9</value>
         <object_component_ref idref="oc-1af"/>
      </symbol>
      <symbol id="sm-27b">
         <name>DL_Timer_setClockConfig</name>
         <value>0x1cad</value>
         <object_component_ref idref="oc-18d"/>
      </symbol>
      <symbol id="sm-27c">
         <name>DL_Timer_initTimerMode</name>
         <value>0x8b5</value>
         <object_component_ref idref="oc-198"/>
      </symbol>
      <symbol id="sm-27d">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x2069</value>
         <object_component_ref idref="oc-16a"/>
      </symbol>
      <symbol id="sm-27e">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x1c91</value>
         <object_component_ref idref="oc-191"/>
      </symbol>
      <symbol id="sm-27f">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x1e89</value>
         <object_component_ref idref="oc-190"/>
      </symbol>
      <symbol id="sm-280">
         <name>DL_Timer_initFourCCPWMMode</name>
         <value>0x6c1</value>
         <object_component_ref idref="oc-18e"/>
      </symbol>
      <symbol id="sm-28d">
         <name>DL_UART_init</name>
         <value>0x156d</value>
         <object_component_ref idref="oc-1a4"/>
      </symbol>
      <symbol id="sm-28e">
         <name>DL_UART_setClockConfig</name>
         <value>0x2023</value>
         <object_component_ref idref="oc-19e"/>
      </symbol>
      <symbol id="sm-29b">
         <name>_c_int00_noargs</name>
         <value>0x1ae1</value>
         <object_component_ref idref="oc-69"/>
      </symbol>
      <symbol id="sm-29c">
         <name>__stack</name>
         <value>0x20203e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-2a8">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x18bd</value>
         <object_component_ref idref="oc-10e"/>
      </symbol>
      <symbol id="sm-2b0">
         <name>_system_pre_init</name>
         <value>0x20c3</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-2bb">
         <name>__TI_zero_init_nomemset</name>
         <value>0x1f13</value>
         <object_component_ref idref="oc-60"/>
      </symbol>
      <symbol id="sm-2c4">
         <name>__TI_decompress_none</name>
         <value>0x2047</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-2cf">
         <name>__TI_decompress_lzss</name>
         <value>0x1285</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-2de">
         <name>abort</name>
         <value>0x20b1</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-2f6">
         <name>HOSTexit</name>
         <value>0x20bb</value>
         <object_component_ref idref="oc-158"/>
      </symbol>
      <symbol id="sm-2f7">
         <name>C$$EXIT</name>
         <value>0x20ba</value>
         <object_component_ref idref="oc-158"/>
      </symbol>
      <symbol id="sm-30c">
         <name>__aeabi_fadd</name>
         <value>0xa8b</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-30d">
         <name>__addsf3</name>
         <value>0xa8b</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-30e">
         <name>__aeabi_fsub</name>
         <value>0xa81</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-30f">
         <name>__subsf3</name>
         <value>0xa81</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-315">
         <name>__muldsi3</name>
         <value>0x1935</value>
         <object_component_ref idref="oc-132"/>
      </symbol>
      <symbol id="sm-31b">
         <name>__aeabi_fmul</name>
         <value>0x1069</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-31c">
         <name>__mulsf3</name>
         <value>0x1069</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-322">
         <name>__aeabi_fdiv</name>
         <value>0x1201</value>
         <object_component_ref idref="oc-e2"/>
      </symbol>
      <symbol id="sm-323">
         <name>__divsf3</name>
         <value>0x1201</value>
         <object_component_ref idref="oc-e2"/>
      </symbol>
      <symbol id="sm-329">
         <name>__aeabi_f2iz</name>
         <value>0x1971</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-32a">
         <name>__fixsfsi</name>
         <value>0x1971</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-330">
         <name>__aeabi_i2f</name>
         <value>0x1809</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-331">
         <name>__floatsisf</name>
         <value>0x1809</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-337">
         <name>__aeabi_fcmpeq</name>
         <value>0x1365</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-338">
         <name>__aeabi_fcmplt</name>
         <value>0x1379</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-339">
         <name>__aeabi_fcmple</name>
         <value>0x138d</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-33a">
         <name>__aeabi_fcmpge</name>
         <value>0x13a1</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-33b">
         <name>__aeabi_fcmpgt</name>
         <value>0x13b5</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-341">
         <name>__aeabi_memcpy</name>
         <value>0x20a9</value>
         <object_component_ref idref="oc-59"/>
      </symbol>
      <symbol id="sm-342">
         <name>__aeabi_memcpy4</name>
         <value>0x20a9</value>
         <object_component_ref idref="oc-59"/>
      </symbol>
      <symbol id="sm-343">
         <name>__aeabi_memcpy8</name>
         <value>0x20a9</value>
         <object_component_ref idref="oc-59"/>
      </symbol>
      <symbol id="sm-34c">
         <name>__eqsf2</name>
         <value>0x18f9</value>
         <object_component_ref idref="oc-137"/>
      </symbol>
      <symbol id="sm-34d">
         <name>__lesf2</name>
         <value>0x18f9</value>
         <object_component_ref idref="oc-137"/>
      </symbol>
      <symbol id="sm-34e">
         <name>__ltsf2</name>
         <value>0x18f9</value>
         <object_component_ref idref="oc-137"/>
      </symbol>
      <symbol id="sm-34f">
         <name>__nesf2</name>
         <value>0x18f9</value>
         <object_component_ref idref="oc-137"/>
      </symbol>
      <symbol id="sm-350">
         <name>__cmpsf2</name>
         <value>0x18f9</value>
         <object_component_ref idref="oc-137"/>
      </symbol>
      <symbol id="sm-351">
         <name>__gtsf2</name>
         <value>0x1845</value>
         <object_component_ref idref="oc-13c"/>
      </symbol>
      <symbol id="sm-352">
         <name>__gesf2</name>
         <value>0x1845</value>
         <object_component_ref idref="oc-13c"/>
      </symbol>
      <symbol id="sm-35e">
         <name>TI_memcpy_small</name>
         <value>0x2035</value>
         <object_component_ref idref="oc-f8"/>
      </symbol>
      <symbol id="sm-35f">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-362">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-363">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
