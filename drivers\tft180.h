//
// TFT180 LCD Driver for MSPM0G3507
// Created for TFT180 1.8" 128x160 ST7735S LCD Display
//

#ifndef TFT180_H
#define TFT180_H

#include "common_inc.h"

// TFT180 引脚定义
#define TFT_RES_PIN     DL_GPIO_PIN_3   // PB3 - 复位引脚
#define TFT_DC_PIN      DL_GPIO_PIN_6   // PB6 - 数据/命令选择引脚
#define TFT_CS_PIN      DL_GPIO_PIN_0   // PB0 - 片选引脚
#define TFT_BL_PIN      DL_GPIO_PIN_1   // PB1 - 背光控制引脚
#define TFT_SDA_PIN     DL_GPIO_PIN_2   // PB2 - SPI数据引脚(MOSI)
#define TFT_SCL_PIN     DL_GPIO_PIN_7   // PB7 - SPI时钟引脚

// TFT180 引脚操作宏
#define TFT_RES(x)      DL_GPIO_writePinsVal(GPIOB, TFT_RES_PIN, (x) ? TFT_RES_PIN : 0)
#define TFT_DC(x)       DL_GPIO_writePinsVal(GPIOB, TFT_DC_PIN, (x) ? TFT_DC_PIN : 0)
#define TFT_CS(x)       DL_GPIO_writePinsVal(GPIOB, TFT_CS_PIN, (x) ? TFT_CS_PIN : 0)
#define TFT_BL(x)       DL_GPIO_writePinsVal(GPIOB, TFT_BL_PIN, (x) ? TFT_BL_PIN : 0)

// TFT180 显示参数
#define TFT_WIDTH       128
#define TFT_HEIGHT      160
#define TFT_XSTART      2
#define TFT_YSTART      1

// ST7735S 命令定义
#define ST7735_NOP      0x00
#define ST7735_SWRESET  0x01
#define ST7735_RDDID    0x04
#define ST7735_RDDST    0x09
#define ST7735_SLPIN    0x10
#define ST7735_SLPOUT   0x11
#define ST7735_PTLON    0x12
#define ST7735_NORON    0x13
#define ST7735_INVOFF   0x20
#define ST7735_INVON    0x21
#define ST7735_DISPOFF  0x28
#define ST7735_DISPON   0x29
#define ST7735_CASET    0x2A
#define ST7735_RASET    0x2B
#define ST7735_RAMWR    0x2C
#define ST7735_RAMRD    0x2E
#define ST7735_PTLAR    0x30
#define ST7735_COLMOD   0x3A
#define ST7735_MADCTL   0x36
#define ST7735_FRMCTR1  0xB1
#define ST7735_FRMCTR2  0xB2
#define ST7735_FRMCTR3  0xB3
#define ST7735_INVCTR   0xB4
#define ST7735_DISSET5  0xB6
#define ST7735_PWCTR1   0xC0
#define ST7735_PWCTR2   0xC1
#define ST7735_PWCTR3   0xC2
#define ST7735_PWCTR4   0xC3
#define ST7735_PWCTR5   0xC4
#define ST7735_VMCTR1   0xC5
#define ST7735_RDID1    0xDA
#define ST7735_RDID2    0xDB
#define ST7735_RDID3    0xDC
#define ST7735_RDID4    0xDD
#define ST7735_PWCTR6   0xFC
#define ST7735_GMCTRP1  0xE0
#define ST7735_GMCTRN1  0xE1

// 颜色定义 (RGB565格式)
#define TFT_BLACK       0x0000
#define TFT_BLUE        0x001F
#define TFT_RED         0xF800
#define TFT_GREEN       0x07E0
#define TFT_CYAN        0x07FF
#define TFT_MAGENTA     0xF81F
#define TFT_YELLOW      0xFFE0
#define TFT_WHITE       0xFFFF
#define TFT_GRAY        0x8410
#define TFT_DARKGRAY    0x4208
#define TFT_LIGHTGRAY   0xC618
#define TFT_ORANGE      0xFD20
#define TFT_PINK        0xF81F
#define TFT_PURPLE      0x8010
#define TFT_BROWN       0xA145

// 函数声明
void tft180_init(void);
void tft180_write_cmd(uint8_t cmd);
void tft180_write_data(uint8_t data);
void tft180_write_data_16bit(uint16_t data);
void tft180_set_address_window(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1);
void tft180_fill_screen(uint16_t color);
void tft180_fill_rect(uint8_t x, uint8_t y, uint8_t w, uint8_t h, uint16_t color);
void tft180_draw_pixel(uint8_t x, uint8_t y, uint16_t color);
void tft180_draw_line(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1, uint16_t color);
void tft180_draw_rect(uint8_t x, uint8_t y, uint8_t w, uint8_t h, uint16_t color);
void tft180_draw_circle(uint8_t x0, uint8_t y0, uint8_t r, uint16_t color);
void tft180_fill_circle(uint8_t x0, uint8_t y0, uint8_t r, uint16_t color);
void tft180_show_char(uint8_t x, uint8_t y, char ch, uint16_t color, uint16_t bg_color);
void tft180_show_string(uint8_t x, uint8_t y, const char *str, uint16_t color, uint16_t bg_color);
void tft180_show_num(uint8_t x, uint8_t y, uint32_t num, uint8_t len, uint16_t color, uint16_t bg_color);
void tft180_show_float(uint8_t x, uint8_t y, float num, uint8_t integer_len, uint8_t decimal_len, uint16_t color, uint16_t bg_color);
uint8_t tft180_printf(uint8_t x, uint8_t y, uint16_t color, uint16_t bg_color, const char *format, ...);

// RGB565颜色转换宏
#define RGB565(r, g, b) (((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3))

#endif // TFT180_H
