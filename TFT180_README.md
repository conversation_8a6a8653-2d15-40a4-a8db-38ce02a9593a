# TFT180 LCD 驱动配置说明

## 硬件连接

根据MSPM0G3507 LaunchPad的引脚布局，TFT180屏幕的连接如下：

| TFT180引脚 | LaunchPad引脚 | 功能说明 |
|-----------|--------------|----------|
| GND       | GND          | 接地 |
| VCC       | 3.3V         | 电源 |
| SCL       | PB7          | SPI时钟线 |
| SDA       | PB2          | SPI数据线(MOSI) |
| RES       | PB3          | 复位引脚 |
| DC        | PB6          | 数据/命令选择 |
| CS        | PB0          | 片选引脚 |
| BL        | PB1          | 背光控制 |

## 软件配置

### 1. SPI配置
- 使用SPI0外设
- 时钟频率：8MHz
- 模式：MOTO3 (SPI模式3)
- 数据位：8位

### 2. GPIO配置
- PB3: TFT_RES (复位)
- PB6: TFT_DC (数据/命令选择)
- PB0: TFT_CS (片选)
- PB1: TFT_BL (背光控制)

### 3. 驱动文件
- `drivers/tft180.h` - 头文件，包含函数声明和宏定义
- `drivers/tft180.c` - 实现文件，包含所有驱动函数

## 主要功能

### 基本显示函数
- `tft180_init()` - 初始化显示屏
- `tft180_fill_screen(color)` - 填充整个屏幕
- `tft180_fill_rect(x, y, w, h, color)` - 填充矩形区域
- `tft180_draw_pixel(x, y, color)` - 画单个像素

### 图形绘制函数
- `tft180_draw_line(x0, y0, x1, y1, color)` - 画直线
- `tft180_draw_rect(x, y, w, h, color)` - 画矩形框
- `tft180_draw_circle(x0, y0, r, color)` - 画圆
- `tft180_fill_circle(x0, y0, r, color)` - 画实心圆

### 文字显示函数
- `tft180_show_char(x, y, ch, color, bg_color)` - 显示单个字符
- `tft180_show_string(x, y, str, color, bg_color)` - 显示字符串
- `tft180_show_num(x, y, num, len, color, bg_color)` - 显示数字
- `tft180_printf(x, y, color, bg_color, format, ...)` - 格式化显示

## 颜色定义

使用RGB565格式，常用颜色宏定义：
- `TFT_BLACK` - 黑色
- `TFT_WHITE` - 白色
- `TFT_RED` - 红色
- `TFT_GREEN` - 绿色
- `TFT_BLUE` - 蓝色
- `TFT_YELLOW` - 黄色
- `TFT_CYAN` - 青色
- `TFT_MAGENTA` - 洋红色

## 使用示例

```c
#include "common_inc.h"

int main(void)
{
    SYSCFG_DL_init();
    
    // 初始化TFT180
    tft180_init();
    
    // 清屏为黑色
    tft180_fill_screen(TFT_BLACK);
    
    // 显示文字
    tft180_show_string(10, 10, "Hello TFT180!", TFT_WHITE, TFT_BLACK);
    
    // 画一个红色矩形
    tft180_fill_rect(10, 30, 50, 20, TFT_RED);
    
    // 画一个蓝色圆圈
    tft180_draw_circle(64, 80, 20, TFT_BLUE);
    
    while(1) {
        // 主循环
    }
}
```

## 注意事项

1. 确保硬件连接正确，特别是电源和接地
2. 初始化TFT180前建议先延时等待系统稳定
3. 字符显示使用6x8像素字体
4. 屏幕分辨率为128x160像素
5. 坐标系原点(0,0)在左上角

## 故障排除

1. **屏幕无显示**：检查电源连接和引脚配置
2. **显示异常**：检查SPI时钟和数据线连接
3. **颜色错误**：确认使用RGB565格式的颜色值
4. **编译错误**：确保包含了所有必要的头文件
