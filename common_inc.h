//
// Created by f<PERSON><PERSON><PERSON> on 2024/7/22.
//

#ifndef COMMON_INC_H
#define COMMON_INC_H

#include <stdio.h>

#include "ti_msp_dl_config.h"

#include "delay.h"
#include "debug.h"
#include "vofa.h"
#include "menu.h"
#include "task.h"
#include "pid.h"
#include "protocol.h"

#include "led.h"
#include "oled.h"
#include "key.h"
#include "motor.h"
#include "encoder.h"
#include "timer.h"
#include "imu.h"
#include "nrf24l01.h"
#include "openmv.h"
#include "flash.h"

extern int16_t target_speed;

void timerA_callback();
void timerB_callback();

#endif //COMMON_INC_H
