//
// Created by faz<PERSON><PERSON> on 2024/3/26.
//

#include "pid.h"

/*!
 * 增量式pid
 * @param pid pid句柄
 * @param p
 * @param i
 * @param d
 * @param threshold
 * @param integral_threshold
 */
void incremental_pid_init(incremental_pid_t *pid, float p, float i, float d, float out_threshold)
{
    pid->P = p;
    pid->I = i;
    pid->D = d;
    pid->out_threshold = out_threshold;
}

/*!
 * 增量式pid
 * @param pid pid句柄
 * @param current_value 当前值
 * @param target_value 目标值
 * @return 输出
 */
float incremental_pid(incremental_pid_t * pid, float current_value, float target_value)
{

    pid->current_error = target_value - current_value;
    pid->p_out = pid->P * (pid->current_error - pid->last_error);
    pid->i_out = pid->I * pid->current_error;
    pid->d_out = pid->D * (pid->current_error - 2*pid->last_error + pid->before_last_error);

    pid->out += pid->p_out + pid->i_out + pid->d_out;

    if (pid->out >= pid->out_threshold)
        pid->out = pid->out_threshold;

    if (pid->out <= -pid->out_threshold)
        pid->out = -pid->out_threshold;
    pid->before_last_error = pid->last_error;
    pid->last_error = pid->current_error;
    return pid->out;
}

/*!
 * 位置式pid
 * @param pid pid句柄
 * @param p
 * @param i
 * @param d
 * @param threshold
 * @param integral_threshold
 */
void position_pid_init(position_pid_t * pid, float p, float i, float d, float out_threshold, float integral_threshold)
{
    pid->P = p;
    pid->I = i;
    pid->D = d;
    pid->out_threshold = out_threshold;
    pid->integral_threshold = integral_threshold;
}

/*!
 * 位置式pid
 * @param pid pid句柄
 * @param current_value 当前值
 * @param target_value 目标值
 * @return 输出
 */
float position_pid(position_pid_t * pid, float current_value, float target_value)
{
    float  filtering_error, current_error;
//    float alpha = 0.7f;
    current_error = target_value - current_value;
//    filtering_error = (1-alpha) * (current_error)  + alpha * pid->last_error;
    filtering_error = current_error;
    pid->integral_error += filtering_error;
    pid->differential_error = current_error - pid->last_error;

    if (pid->integral_error >= pid->integral_threshold)
        pid->integral_error = pid->integral_threshold;

    if (pid->integral_error <= -pid->integral_threshold)
        pid->integral_error = -pid->integral_threshold;

    float out = pid->P * current_error + pid->I * pid->integral_error + pid->D * pid->differential_error;

    if (out >= pid->out_threshold)
        out = pid->out_threshold;

    if (out <= -pid->out_threshold)
        out = -pid->out_threshold;

    pid->last_error=current_error;

    return out;
}
