/*
 * Copyright (c) 2023, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "common_inc.h"

incremental_pid_t left_speed_pid;
incremental_pid_t right_speed_pid;

int16_t target_speed;

int main(void)
{
    SYSCFG_DL_init();

    // 延时等待系统稳定
    delay_ms(100);

    // 初始化TFT180显示屏
    tft180_init();

    // 简单测试：填充屏幕为红色
    tft180_fill_screen(TFT_RED);
    delay_ms(1000);

    // 填充屏幕为绿色
    tft180_fill_screen(TFT_GREEN);
    delay_ms(1000);

    // 填充屏幕为蓝色
    tft180_fill_screen(TFT_BLUE);
    delay_ms(1000);

    // 清屏为黑色
    tft180_fill_screen(TFT_BLACK);

    // 显示测试文字
    tft180_show_string(10, 10, "TFT180 OK!", TFT_WHITE, TFT_BLACK);

    uint16_t counter = 0;

    while (1) {
        // 显示计数器
        tft180_printf(10, 30, TFT_CYAN, TFT_BLACK, "Count: %d", counter++);

        // 直接设置电机占空比：左轮2000，右轮2000
        motor_set_duty(2000, 2000);
        delay_ms(500);
    }
}

// 10ms
void timerA_callback()
{
    encoder_callback();
    int16_t left_duty = (int16_t)incremental_pid(&left_speed_pid, left_speed, target_speed);
    int16_t right_duty = (int16_t)incremental_pid(&right_speed_pid, right_speed, target_speed);
    motor_set_duty(left_duty, right_duty);
//    vofa_add_data(left_speed);
//    vofa_add_data(right_speed);
//    vofa_add_data(target_speed);
//    vofa_send();
}

// 15ms
void timerB_callback()
{
//    printf("timerB\n");
}

void GROUP1_IRQHandler(void)
{
    encoder_exti_callback();
    nrf24l01_receive_callback();
}
